<!DOCTYPE html>
<html>
<head>
    <title>API Test</title>
</head>
<body>
    <h1>API Test</h1>
    <div id="result"></div>
    
    <script>
        async function testAPI() {
            try {
                const response = await fetch('http://localhost:8080/api/project/getPrjInfo/2369');
                const data = await response.json();
                document.getElementById('result').innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
            } catch (error) {
                document.getElementById('result').innerHTML = 'Error: ' + error.message;
            }
        }
        
        testAPI();
    </script>
</body>
</html>
