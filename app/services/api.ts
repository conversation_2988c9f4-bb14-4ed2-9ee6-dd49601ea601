
// @/services/api.ts
const API_BASE_URL = 'http://localhost:8080/api/project';

// A generic fetch function to reduce boilerplate
async function fetchData(endpoint: string) {
    try {
        const response = await fetch(`${API_BASE_URL}/${endpoint}`);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const data = await response.json();
        console.log(`API Response for ${endpoint}:`, data); // 添加调试日志
        return data;
    } catch (error) {
        console.error(`Failed to fetch ${endpoint}:`, error);
        return null;
    }
}

export const getProjectInfo = (id: string) => fetchData(`getPrjInfo/${id}`);
export const getRepaymentInfo = (id: string) => fetchData(`repayment/${id}`);
export const getDigitalAssets = (id: string) => fetchData(`attachments/${id}`);
export const getWorkflows = (id: string) => fetchData(`workflows/${id}`);
export const getTaskAssignment = (id: string) => fetchData(`task-assignment/${id}`);
export const getRelatedFees = (id: string) => fetchData(`fees/${id}`);
export const getProjectProgress = (id: string) => fetchData(`progress/${id}`);
