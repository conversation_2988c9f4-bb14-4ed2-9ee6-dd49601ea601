import { NextRequest } from 'next/server';

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const endpoint = searchParams.get('endpoint');

  if (!endpoint) {
    return new Response('Missing endpoint parameter', { status: 400 });
  }

  try {
    const apiBaseUrl = process.env.API_BASE_URL || 'http://localhost:8080/api/project';
    const targetUrl = `${apiBaseUrl}/${endpoint}`;
    
    console.log(`Proxying request to: ${targetUrl}`);
    
    const response = await fetch(targetUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...request.headers,
      },
    });
    
    if (!response.ok) {
      console.error(`Upstream API error: ${response.status} ${response.statusText}`);
      console.error(`Requested URL: ${targetUrl}`);
      return new Response(`Upstream API error: ${response.status} ${response.statusText}`, { 
        status: response.status 
      });
    }
    
    const data = await response.json();
    return new Response(JSON.stringify(data), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
    });
  } catch (error: any) {
    console.error('Proxy error:', error);
    console.error('This might be due to:');
    console.error('1. The backend server is not running');
    console.error('2. The backend server is running on a different port');
    console.error('3. Network connectivity issues');
    console.error('4. Incorrect API_BASE_URL environment variable');
    
    return new Response(
      JSON.stringify({ 
        error: 'Failed to fetch data from backend API', 
        message: error.message,
        tip: 'Make sure the backend API is running on http://localhost:8080'
      }), 
      { 
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
}