'use client';

import React, { useEffect, useState } from 'react';
import { getDigitalAssets } from '@/services/api';
import { Paperclip } from 'lucide-react';

interface ProjectDigitalAssetsProps {
    projectId: string;
}

const ProjectDigitalAssets: React.FC<ProjectDigitalAssetsProps> = ({ projectId }) => {
    const [data, setData] = useState<any[]>([]);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        if (projectId) {
            getDigitalAssets(projectId).then(data => {
                setData(data || []);
                setLoading(false);
            });
        }
    }, [projectId]);

    if (loading) return <div className="text-center text-sm">加载中...</div>;
    if (!data || data.length === 0) return <div className="text-center text-sm text-muted-foreground">无相关数字资产</div>;

    return (
        <div className="text-sm">
            <ul className="space-y-3">
                {data.map((asset, index) => (
                     <li key={index}>
                        <a href={asset.attachmentUrl} target="_blank" rel="noopener noreferrer" 
                           className="flex items-center p-2 -m-2 rounded-md transition-colors hover:bg-muted/50">
                            <Paperclip className="h-5 w-5 mr-3 text-muted-foreground flex-shrink-0" />
                            <div className="flex-grow">
                                <p className="font-medium text-foreground truncate">{asset.attachmentName}</p>
                                <p className="text-xs text-muted-foreground">{asset.workflowName}</p>
                            </div>
                        </a>
                    </li>
                ))}
            </ul>
        </div>
    );
};

export default ProjectDigitalAssets;