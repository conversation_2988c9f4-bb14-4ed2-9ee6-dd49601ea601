// zdhj-prjboard-web/app/components/ProjectMilestones.tsx
"use client";

import React from 'react';

const ProjectMilestones = () => {
  return (
    <div className="module">
      <header>
        <h1 className="biaoti">里程碑/数字资产</h1>
      </header>

      <div className="timeline-container">
        <div className="timeline">
          {/* 投标节点 */}
          <div className="timeline-node">
            <div className="node-header">
              <div className="node-title">投标</div>
              <div className="node-date">2022.12.20</div>
            </div>
            <div className="file-card">
              <div className="file-title">
                【中标通知书】：ZD2208154-ZJZX吉利汽车...
              </div>
            </div>
          </div>

          {/* 合同签订节点 */}
          <div className="timeline-node">
            <div className="node-header">
              <div className="node-title">合同签订</div>
              <div className="node-date">2022.12.20</div>
            </div>
            <div className="file-card">
              <div className="file-title">
                【合同文件】：吉利汽车集团有限公司及下属...
              </div>
            </div>
          </div>

          {/* 项目启动节点 */}
          <div className="timeline-node">
            <div className="node-header">
              <div className="node-title">项目启动</div>
              <div className="node-date">2022.12.20</div>
            </div>
            <div className="file-card">
              <div className="file-title">
                【竣工验收报告】：吉利汽车集团有限公司及...
              </div>
            </div>
            <div className="file-card">
              <div className="file-title">
                【报告书】：吉利汽车集团有限公司及下属及...
              </div>
            </div>
          </div>

          {/* 项目总结节点 */}
          <div className="timeline-node">
            <div className="node-header">
              <div className="node-title">项目总结</div>
              <div className="node-date">2022.12.20</div>
            </div>
            <div className="file-card">
              <div className="file-title">
                【项目总结】：吉利汽车集团有限公司及下属...
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProjectMilestones;
