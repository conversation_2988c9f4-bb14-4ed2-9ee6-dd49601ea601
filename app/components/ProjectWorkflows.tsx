// zdhj-prjboard-web/app/components/ProjectWorkflows.tsx
"use client";

import React, { useEffect, useState } from 'react';
import { getWorkflows } from '@/services/api';

interface ProjectWorkflowsProps {
  projectId: string;
}

const ProjectWorkflows: React.FC<ProjectWorkflowsProps> = ({ projectId }) => {
  const [data, setData] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (projectId) {
      getWorkflows(projectId).then(response => {
        // 转换后端数据结构为前端期望的格式
        const transformedData = response?.map((workflow: any) => ({
          workflowName: workflow.workflowType,
          details: workflow.workflowTitle,
          link: workflow.workflowLink
        })) || [];
        setData(transformedData);
        setLoading(false);
      });
    }
  }, [projectId]);

  if (loading) return <div className="module">加载中...</div>;
  if (!data || data.length === 0) return <div className="module">无相关流程数据</div>;

  return (
    <div className="module">
      <div className="module-header">
        <h1 className="module-title">相关流程</h1>
      </div>

      <div className="module-content">
        <div className="process-list">
          {data.map((workflow, index) => (
            <div className="process-card" key={index}>
              <div className="process-content">
                <div className="process-title">
                  {workflow.link ? (
                    <a href={workflow.link} target="_blank" rel="noopener noreferrer" className="workflow-link">
                      {workflow.workflowName}
                    </a>
                  ) : (
                    workflow.workflowName
                  )}
                </div>
                <div className="process-details">{workflow.details}</div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ProjectWorkflows;