// zdhj-prjboard-web/app/components/ProjectWorkflows.tsx
"use client";

import React, { useEffect, useState } from 'react';
import { getWorkflows } from '@/services/api';

interface ProjectWorkflowsProps {
  projectId: string;
}

const ProjectWorkflows: React.FC<ProjectWorkflowsProps> = ({ projectId }) => {
  const [data, setData] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (projectId) {
      getWorkflows(projectId).then(response => {
        setData(response.data || []);
        setLoading(false);
      });
    }
  }, [projectId]);

  if (loading) return <div className="column column-1">加载中...</div>;
  if (!data || data.length === 0) return <div className="column column-1">无相关流程数据</div>;

  return (
    <div className="column column-1">
      <div className="header">
        <h1>项目相关流程</h1>
      </div>

      <div className="process-list">
        {data.map((workflow, index) => (
          <div className="process-card" key={index}>
            <div className="process-content">
              <div className="process-title">{workflow.workflowName}</div>
              <div className="process-details">{workflow.details}</div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ProjectWorkflows;