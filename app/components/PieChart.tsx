'use client';

import React, { useState } from 'react';

interface PieChartProps {
    data: { name: string; value: number; color?: string }[];
    title?: string;
    centerText?: string;
}

const PieChart: React.FC<PieChartProps> = ({ data, title, centerText }) => {
    const [hoveredSlice, setHoveredSlice] = useState<{ name: string; value: number } | null>(null);
    const [tooltipPos, setTooltipPos] = useState<{ x: number; y: number }>({ x: 0, y: 0 });

    const defaultColors = [
        '#87CEEB', // Sky Blue
        '#FFDAB9', // Peach Puff
        '#98FB98', // Pale Green
        '#F08080', // Light Coral
        '#DDA0DD', // Plum
        '#ADD8E6', // Light Blue
    ];

    if (!data || data.length === 0) {
        return <div className="text-center text-gray-500 p-4">数据不足</div>;
    }

    const total = data.reduce((acc, item) => acc + item.value, 0);

    if (total === 0) {
        return <div className="text-center text-gray-500 p-4">数据总量为零</div>;
    }

    const radius = 50;
    const innerRadius = 30; // This creates the donut hole
    const centerX = 50;
    const centerY = 50;
    let currentAngle = -0.25; // Start at the top

    const getCoordinates = (percent: number, r: number) => {
        const x = centerX + r * Math.cos(2 * Math.PI * percent);
        const y = centerY + r * Math.sin(2 * Math.PI * percent);
        return { x, y };
    };

    const slices = data.map((item, index) => {
        const percentage = item.value / total;
        const startAngle = currentAngle;
        const endAngle = currentAngle + percentage;

        const startOuter = getCoordinates(startAngle, radius);
        const endOuter = getCoordinates(endAngle, radius);
        const startInner = getCoordinates(startAngle, innerRadius);
        const endInner = getCoordinates(endAngle, innerRadius);

        const largeArcFlag = percentage > 0.5 ? 1 : 0;

        const pathData = [
            `M ${startOuter.x},${startOuter.y}`,
            `A ${radius},${radius} 0 ${largeArcFlag} 1 ${endOuter.x},${endOuter.y}`,
            `L ${endInner.x},${endInner.y}`,
            `A ${innerRadius},${innerRadius} 0 ${largeArcFlag} 0 ${startInner.x},${startInner.y}`,
            `Z`,
        ].join(' ');

        currentAngle = endAngle;

        const color = item.color || defaultColors[index % defaultColors.length];

        return {
            pathData,
            color,
            name: item.name,
            value: item.value,
            percentage: percentage * 100,
        };
    });

    const handleMouseMove = (e: React.MouseEvent) => {
        const rect = e.currentTarget.getBoundingClientRect();
        setTooltipPos({ x: e.clientX - rect.left, y: e.clientY - rect.top });
    };

    return (
        <div className="flex flex-col items-center justify-center w-full h-full">
            {title && <h3 className="text-md font-semibold mb-2">{title}</h3>}
            <div className="relative w-full h-full max-w-[150px] max-h-[150px]">
                <svg width="100%" height="100%" viewBox="0 0 100 100"
                    onMouseLeave={() => setHoveredSlice(null)}
                    onMouseMove={handleMouseMove}
                >
                    {slices.map((slice, index) => (
                        <path
                            key={index}
                            d={slice.pathData}
                            fill={slice.color}
                            className="transition-opacity duration-200" 
                            style={{opacity: hoveredSlice && hoveredSlice.name !== slice.name ? 0.5 : 1}}
                            onMouseEnter={() => setHoveredSlice(slice)}
                        />
                    ))}
                     {centerText && (
                        <text
                            className="fill-gray-800 text-lg font-bold"
                            x="50%"
                            y="50%"
                            dy=".3em"
                            textAnchor="middle">
                            {centerText}
                        </text>
                    )}
                </svg>
                 {hoveredSlice && (
                    <div
                        className="absolute bg-gray-900 text-white text-xs p-2 rounded-md shadow-lg z-10 pointer-events-none transition-opacity duration-200"
                        style={{ left: tooltipPos.x, top: tooltipPos.y, transform: 'translate(10px, -100%)' }}
                    >
                        {hoveredSlice.name}: {hoveredSlice.value.toLocaleString(undefined, {minimumFractionDigits: 2})}
                    </div>
                )}
            </div>
        </div>
    );
};

export default PieChart;
