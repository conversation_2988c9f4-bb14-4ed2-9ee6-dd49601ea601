// zdhj-prjboard-web/app/components/ProjectTaskAssignment.tsx
"use client";

import React, { useEffect, useState } from 'react';
import { getTaskAssignment } from '@/services/api';

interface ProjectTaskAssignmentProps {
  projectId: string;
}

const ProjectTaskAssignment: React.FC<ProjectTaskAssignmentProps> = ({ projectId }) => {
  const [data, setData] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (projectId) {
      getTaskAssignment(projectId).then(response => {
        setData(response.data);
        setLoading(false);
      });
    }
  }, [projectId]);

  if (loading) return <div className="module">加载中...</div>;
  if (!data) return <div className="module">无法加载任务分配信息</div>;

  return (
    <div className="module">
      <div className="task-assignment-container">
        <div className="container">
          <div className="header">
            <h1 className="title">项目任务分配</h1>
          </div>

          <div className="content">
            <div className="info-section">
              <div className="info-row">
                <div className="info-label">执业团队：</div>
                <div className="info-value">
                  <div className="team-members">
                    {data.teamMembers && data.teamMembers.map((member: string, index: number) => (
                      <span className="member-tag" key={index}>{member}</span>
                    ))}
                  </div>
                </div>
              </div>

              <div className="info-row">
                <div className="info-label">项目经理：</div>
                <div className="info-value">
                  <span className="member-tag">{data.projectManager}</span>
                </div>
              </div>

              <div className="info-row">
                <div className="info-label">项目组成员：</div>
                <div className="info-value">
                  <div className="team-members">
                    {data.projectMembers && data.projectMembers.map((member: string, index: number) => (
                      <span className="member-tag" key={index}>{member}</span>
                    ))}
                  </div>
                </div>
              </div>

              <div className="info-row">
                <div className="info-label">人员变更：</div>
                <div className="info-value change-info">
                  {data.memberChange}
                </div>
              </div>
            </div>

            <div className="subtasks-section">
              <h2 className="subtasks-title">子任务列表</h2>
              <ul className="task-list">
                {data.subTasks && data.subTasks.map((task: any, index: number) => (
                  <li className="task-item" key={index}>
                    <div className="task-name">{task.taskName}</div>
                    <div className="task-detail">
                      <span className="detail-label">
                        任务成员：{task.members.join(' ')}
                      </span>
                    </div>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProjectTaskAssignment;