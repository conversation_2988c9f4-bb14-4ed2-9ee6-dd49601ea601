'use client';

import { useState, useEffect } from 'react';
import { getTaskAssignment } from '@/services/api';

interface ProjectTaskAssignmentProps {
  projectId: string;
}

export default function ProjectTaskAssignment({ projectId }: ProjectTaskAssignmentProps) {
  const [data, setData] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (projectId) {
      getTaskAssignment(projectId).then(response => {
        // 转换后端中文字段名为前端期望的英文字段名
        const transformedData = {
          teamMembers: response["执业团队"] ? response["执业团队"].split(',') : [],
          projectManager: response["项目经理"] || "",
          projectMembers: response["项目成员"] ? response["项目成员"].split(',') : [],
          memberChange: response["人员变更"] || [],
          subTasks: response["子任务列表"]?.map((task: any) => ({
            taskName: task.subject,
            members: task["任务成员"] ? task["任务成员"].split(',') : [],
            owner: task["任务负责人"],
            endDate: task.enddate,
            link: task.link
          })) || []
        };
        setData(transformedData);
        setLoading(false);
      });
    }
  }, [projectId]);

  if (loading) {
    return <div className="loading">加载中...</div>;
  }

  if (!data) {
    return <div className="error">暂无数据</div>;
  }

  return (
    <div className="module">
      <div className="module-header">
        <h1 className="module-title">项目任务分配</h1>
      </div>

      <div className="module-content">
        <div className="task-assignment-container">
          <div className="project-info">
            <h2>项目信息</h2>
            <div className="info-grid">
              <div className="info-section">
                <div className="info-row">
                  <div className="info-label">执业团队：</div>
                  <div className="info-value">
                    <div className="team-members">
                      {data.teamMembers && data.teamMembers.map((member: string, index: number) => (
                        <span className="member-tag" key={index}>{member}</span>
                      ))}
                    </div>
                  </div>
                </div>

                <div className="info-row">
                  <div className="info-label">项目经理：</div>
                  <div className="info-value">
                    <span className="member-tag">{data.projectManager}</span>
                  </div>
                </div>

                <div className="info-row">
                  <div className="info-label">项目组成员：</div>
                  <div className="info-value">
                    <div className="team-members">
                      {data.projectMembers && data.projectMembers.map((member: string, index: number) => (
                        <span className="member-tag" key={index}>{member}</span>
                      ))}
                    </div>
                  </div>
                </div>

                <div className="info-row">
                  <div className="info-label">人员变更：</div>
                  <div className="info-value change-info">
                    {data.memberChange && data.memberChange.length > 0 ? (
                      <div className="change-list">
                        {data.memberChange.map((change: any, index: number) => (
                          <div key={index} className="change-item">
                            <div><strong>{change["任务名称"]}</strong></div>
                            <div>原负责人: {change["原任务负责人"]} → 新负责人: {change["新任务负责人"]}</div>
                            {(change["项目成员"] || change["新项目成员"]) && (
                              <div>
                                原任务成员: {change["项目成员"] || "无"} → 新任务成员: {change["新项目成员"] || "无"}
                              </div>
                            )}
                            {change["变更日期"] && (
                              <div className="change-date">变更日期: {change["变更日期"]}</div>
                            )}
                          </div>
                        ))}
                      </div>
                    ) : (
                      "无变更"
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="subtasks-section">
            <div className="section-header">
              <h2 className="subtasks-title">
                <span className="title-icon">📋</span>
                子任务列表
                <span className="task-count">({data.subTasks?.length || 0})</span>
              </h2>
            </div>
            <div className="subtasks-grid">
              {data.subTasks && data.subTasks.length > 0 ? (
                data.subTasks.map((task: any, index: number) => (
                  <div className="subtask-card" key={index}>
                    <div className="subtask-header">
                      <div className="task-index">#{index + 1}</div>
                      <div className="task-name">
                        {task.link ? (
                          <a href={task.link} target="_blank" rel="noopener noreferrer" className="task-link">
                            <span className="link-icon">🔗</span>
                            {task.taskName}
                          </a>
                        ) : (
                          task.taskName
                        )}
                      </div>
                    </div>

                    {task.owner && (
                      <div className="task-owner-section">
                        <div className="owner-label">
                          <span className="owner-icon">👤</span>
                          任务负责人
                        </div>
                        <div className="task-owner">
                          <span className="owner-tag">{task.owner}</span>
                        </div>
                      </div>
                    )}

                    {task.members && task.members.length > 0 && (
                      <div className="task-members-section">
                        <div className="members-label">
                          <span className="members-icon">👥</span>
                          任务成员
                        </div>
                        <div className="task-members">
                          {task.members.map((member: string, memberIndex: number) => (
                            <span key={memberIndex} className="member-tag">{member}</span>
                          ))}
                        </div>
                      </div>
                    )}

                    {task.endDate && (
                      <div className="task-date">
                        <span className="date-icon">📅</span>
                        计划完成: {task.endDate}
                      </div>
                    )}
                  </div>
                ))
              ) : (
                <div className="no-tasks">
                  <span className="no-tasks-icon">📝</span>
                  暂无子任务
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
