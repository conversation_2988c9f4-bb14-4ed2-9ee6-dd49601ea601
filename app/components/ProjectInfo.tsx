'use client';

import React, { useEffect, useState } from 'react';
import { getProjectInfo } from '@/services/api';

interface ProjectInfoProps {
    projectId: string;
}

const ProjectInfo: React.FC<ProjectInfoProps> = ({ projectId }) => {
    const [data, setData] = useState<any>(null);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        if (projectId) {
            getProjectInfo(projectId).then(data => {
                setData(data);
                setLoading(false);
            });
        }
    }, [projectId]);

    if (loading) return <div className="text-center text-sm">加载中...</div>;
    if (!data) return <div className="text-center text-red-500 text-sm">无法加载项目信息</div>;

    return (
        <div className="text-sm">
            <dl className="grid grid-cols-[auto_1fr] gap-x-4 gap-y-2">
                {Object.entries(data).map(([key, value]) => {
                    // Don't render if value is null, undefined, or an empty string
                    if (value === null || value === undefined || value === '') {
                        return null;
                    }
                    return (
                        <React.Fragment key={key}>
                            <dt className="font-semibold text-muted-foreground whitespace-nowrap">{key}</dt>
                            <dd className="text-foreground truncate">{String(value)}</dd>
                        </React.Fragment>
                    );
                })}
            </dl>
        </div>
    );
};

export default ProjectInfo;