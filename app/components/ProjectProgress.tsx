// zdhj-prjboard-web/app/components/ProjectProgress.tsx
"use client";

import React, { useEffect, useState } from 'react';
import { getProjectProgress } from '@/services/api';

interface ProjectProgressProps {
  projectId: string;
}

const ProjectProgress: React.FC<ProjectProgressProps> = ({ projectId }) => {
  const [data, setData] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (projectId) {
      getProjectProgress(projectId).then(response => {
        setData(response.data);
        setLoading(false);
      });
    }
  }, [projectId]);

  if (loading) return <div className="column column-3">Loading...</div>;
  if (!data) return <div className="column column-3">Could not load project progress.</div>;

  const getBarClass = (status: string) => {
    switch (status) {
      case '正常推进中':
        return 'bar-normal';
      case '异常暂停中':
        return 'bar-paused';
      case '异常中止中':
        return 'bar-stopped';
      default:
        return 'bar-total';
    }
  };

  return (
    <div className="column column-3">
      <div className="chart-header">
        <h1>项目进度</h1>
        <div className="chart-legend">
          <div className="legend-item">
            <div className="legend-color legend-normal"></div>
            <span>正常推进中</span>
          </div>
          <div className="legend-item">
            <div className="legend-color legend-paused"></div>
            <span>异常暂停中</span>
          </div>
          <div className="legend-item">
            <div className="legend-color legend-stopped"></div>
            <span>异常中止中</span>
          </div>
          <div className="legend-item">
            <div className="legend-color legend-total"></div>
            <span>项目总进度</span>
          </div>
        </div>
      </div>

      <div className="chart-body">
        {/* Project Total Progress */}
        <div className="chart-grid">
          <div className="row-label">项目总进度</div>
          <div className="progress-container">
            <div
              className="progress-bar bar-total"
              style={{ width: `${data.totalProgress}%` }}
            >
              {data.totalProgress}%
            </div>
          </div>
          <div className="percentage-scale">{data.totalProgress}%</div>
        </div>

        {/* Task Progress Bars */}
        {data.tasks && data.tasks.map((task: any, index: number) => (
          <div className="chart-grid" key={index}>
            <div className="row-label">{task.taskName}</div>
            <div className="progress-container">
              <div
                className={`progress-bar ${getBarClass(task.status)}`}
                style={{ width: `${task.progress}%` }}
              >
                {task.progress}%
              </div>
            </div>
            <div className="percentage-scale">{task.progress}%</div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ProjectProgress;