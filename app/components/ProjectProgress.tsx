// zdhj-prjboard-web/app/components/ProjectProgress.tsx
"use client";

import React, { useEffect, useState } from 'react';
import { getProjectProgress } from '@/services/api';

interface ProjectProgressProps {
  projectId: string;
}

const ProjectProgress: React.FC<ProjectProgressProps> = ({ projectId }) => {
  const [data, setData] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (projectId) {
      getProjectProgress(projectId).then(response => {
        // 转换后端中文字段名为前端期望的英文字段名
        const transformedData = {
          // 确保进度值在0-100范围内
          totalProgress: Math.round((response["项目进度"] || 0) * (response["项目进度"] <= 1 ? 100 : 1)),
          tasks: response.tasks?.map((task: any) => ({
            taskName: task["任务名称"],
            // 确保任务进度值在0-100范围内
            progress: Math.round((task["任务进度"] || 0) * (task["任务进度"] <= 1 ? 100 : 1)),
            status: "任务进度" // 后端没有返回状态，设置默认值
          })) || []
        };
        setData(transformedData);
        setLoading(false);
      });
    }
  }, [projectId]);

  if (loading) return <div className="column column-3">Loading...</div>;
  if (!data) return <div className="column column-3">Could not load project progress.</div>;

  const getBarClass = (status: string) => {
    switch (status) {
      case '任务进度':
        return 'bar-normal';
      default:
        return 'bar-total';
    }
  };

  return (
    <div className="module">
      <div className="module-header">
        <h1 className="module-title">项目进度</h1>
        <div className="chart-legend">
          <div className="legend-item">
            <div className="legend-color legend-total"></div>
            <span>项目总进度</span>
          </div>
          <div className="legend-item">
            <div className="legend-color legend-normal"></div>
            <span>任务进度</span>
          </div>

        </div>
      </div>

      <div className="module-content">
        <div className="chart-body">
          {/* Project Total Progress */}
          <div className="chart-grid">
            <div className="row-label">项目总进度</div>
            <div className="progress-container">
              <div
                className="progress-bar bar-total"
                style={{ width: `${Math.min(data.totalProgress, 100)}%` }}
              >
                {data.totalProgress}%
              </div>
            </div>
            <div className="percentage-scale">{data.totalProgress}%</div>
          </div>

          {/* Task Progress Bars */}
          {data.tasks && data.tasks.map((task: any, index: number) => (
            <div className="chart-grid" key={index}>
              <div className="row-label" title={task.taskName}>
                {task.taskName.length > 8 ? `${task.taskName.substring(0, 8)}...` : task.taskName}
              </div>
              <div className="progress-container">
                <div
                  className={`progress-bar ${getBarClass(task.status)}`}
                  style={{ width: `${Math.min(task.progress, 100)}%` }}
                >
                  {task.progress}%
                </div>
              </div>
              <div className="percentage-scale">{task.progress}%</div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ProjectProgress;