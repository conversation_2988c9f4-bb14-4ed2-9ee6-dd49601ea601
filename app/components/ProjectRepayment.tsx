// zdhj-prjboard-web/app/components/ProjectRepayment.tsx
"use client";

import React, { useEffect, useRef, useState } from 'react';
import { getRepaymentInfo } from '@/services/api';

interface ProjectRepaymentProps {
  projectId: string;
}

const ProjectRepayment: React.FC<ProjectRepaymentProps> = ({ projectId }) => {
  const chartRef = useRef<HTMLCanvasElement>(null);
  const [data, setData] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (projectId) {
      getRepaymentInfo(projectId).then(response => {
        // 转换后端数据结构为前端期望的格式
        const transformedData = {
          contractAmount: response.amountInfo?.contractAmount || 0,
          repaymentAmount: response.amountInfo?.collectedAmount || 0,
          unRepaymentAmount: (response.amountInfo?.contractAmount || 0) - (response.amountInfo?.collectedAmount || 0),
          invoiceAmount: response.amountInfo?.invoiceAmount || 0,
          totalRepaymentAmount: response.amountInfo?.contractAmount || 0,
          planList: response.dateInfo?.plannedCollectionDates?.map((item: any) => ({
            taskName: item.subject,
            repaymentDate: item.date
          })) || [],
          invoiceWorkflows: response.workflowInfo?.invoiceWorkflows?.map((workflow: any) => ({
            workflowType: workflow.workflowType,
            workflowTitle: workflow.workflowTitle,
            workflowLink: workflow.workflowLink
          })) || []
        };
        setData(transformedData);
        setLoading(false);
      });
    }
  }, [projectId]);

  useEffect(() => {
    if (data && chartRef.current && (window as any).Chart) {
      const ctx = chartRef.current.getContext('2d');
      if (ctx) {
        let chartStatus = (window as any).Chart.getChart(chartRef.current);
        if (chartStatus != undefined) {
          chartStatus.destroy();
        }

        const { Chart } = (window as any);
        new Chart(ctx, {
          type: 'doughnut',
          data: {
            datasets: [
              {
                data: [data.repaymentAmount, data.unRepaymentAmount],
                backgroundColor: ['#1890ff', '#e8e8e8'],
                borderWidth: 0,
              },
            ],
          },
          options: {
            cutout: '80%',
            plugins: {
              legend: {
                display: false,
              },
              tooltip: {
                enabled: false,
              },
            },
            responsive: true,
            maintainAspectRatio: false,
          },
        });
      }
    }
  }, [data]);

  if (loading) return <div className="module">加载中...</div>;
  if (!data) return <div className="module">无法加载项目回款信息</div>;

  const repaymentPercentage = data.totalRepaymentAmount ? 
    Math.round((data.repaymentAmount / data.totalRepaymentAmount) * 100) : 0;

  return (
    <div className="module">
      <div className="module-header">
        <h1 className="module-title">开票回款</h1>
      </div>

      <div className="module-content">
        <div className="payment-progress">
          <div className="chart-container">
            <canvas ref={chartRef} id="donutChart" width="140" height="140"></canvas>
            <div className="chart-center">
              <div className="percentage">{repaymentPercentage}%</div>
            </div>
          </div>

          <div className="payment-info">
            <div className="amount-row">
              <span className="amount-label">合同金额</span>
              <span className="amount-value total">{data.contractAmount}元</span>
            </div>
            <div className="amount-row">
              <span className="amount-label">已回款</span>
              <span className="amount-value paid">{data.repaymentAmount}元</span>
            </div>
            <div className="amount-row">
              <span className="amount-label">未回款</span>
              <span className="amount-value unpaid">{data.unRepaymentAmount}元</span>
            </div>
            <div className="amount-row">
              <span className="amount-label">已开票</span>
              <span className="amount-value invoice">{data.invoiceAmount}元</span>
            </div>
          </div>
        </div>

        <div className="invoice-workflows">
          <h2 className="section-title">开票流程</h2>
          {data.invoiceWorkflows && data.invoiceWorkflows.length > 0 ? (
            <div className="workflow-list">
              {data.invoiceWorkflows.map((workflow: any, index: number) => (
                <div className="workflow-item" key={index}>
                  <div className="workflow-title">
                    {workflow.workflowLink ? (
                      <a href={workflow.workflowLink} target="_blank" rel="noopener noreferrer" className="workflow-link">
                        {workflow.workflowTitle}
                      </a>
                    ) : (
                      workflow.workflowTitle
                    )}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="no-data">暂无开票流程</div>
          )}
        </div>

        <div className="payment-plan">
          <h2 className="section-title">计划回款日期</h2>
          <ul className="plan-list">
            {data.planList && data.planList.map((item: any, index: number) => (
              <li className="plan-item" key={index}>
                <span className="task-name">{item.taskName}</span>
                <span className="task-date">{item.repaymentDate}</span>
              </li>
            ))}
          </ul>
        </div>
      </div>
    </div>
  );
};

export default ProjectRepayment;