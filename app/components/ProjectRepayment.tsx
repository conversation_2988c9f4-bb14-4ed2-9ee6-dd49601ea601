// zdhj-prjboard-web/app/components/ProjectRepayment.tsx
"use client";

import React, { useEffect, useRef, useState } from 'react';
import { getRepaymentInfo } from '@/services/api';

interface ProjectRepaymentProps {
  projectId: string;
}

const ProjectRepayment: React.FC<ProjectRepaymentProps> = ({ projectId }) => {
  const chartRef = useRef<HTMLCanvasElement>(null);
  const [data, setData] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (projectId) {
      getRepaymentInfo(projectId).then(response => {
        setData(response.data);
        setLoading(false);
      });
    }
  }, [projectId]);

  useEffect(() => {
    if (data && chartRef.current && (window as any).Chart) {
      const ctx = chartRef.current.getContext('2d');
      if (ctx) {
        let chartStatus = (window as any).Chart.getChart(chartRef.current);
        if (chartStatus != undefined) {
          chartStatus.destroy();
        }

        const { Chart } = (window as any);
        new Chart(ctx, {
          type: 'doughnut',
          data: {
            datasets: [
              {
                data: [data.repaymentAmount, data.unRepaymentAmount],
                backgroundColor: ['#1890ff', '#e8e8e8'],
                borderWidth: 0,
              },
            ],
          },
          options: {
            cutout: '80%',
            plugins: {
              legend: {
                display: false,
              },
              tooltip: {
                enabled: false,
              },
            },
            responsive: true,
            maintainAspectRatio: false,
          },
        });
      }
    }
  }, [data]);

  if (loading) return <div className="module">加载中...</div>;
  if (!data) return <div className="module">无法加载项目回款信息</div>;

  const repaymentPercentage = data.totalRepaymentAmount ? 
    Math.round((data.repaymentAmount / data.totalRepaymentAmount) * 100) : 0;

  return (
    <div className="module">
      <div className="header">
        <h1 className="title">项目回款</h1>
      </div>

      <div className="payment-progress">
        <div className="chart-container">
          <canvas ref={chartRef} id="donutChart" width="140" height="140"></canvas>
          <div className="chart-center">
            <div className="percentage">{repaymentPercentage}%</div>
          </div>
        </div>

        <div className="payment-info">
          <div className="amount-row">
            <span className="amount-label">已回款</span>
            <span className="amount-value paid">{data.repaymentAmount}元</span>
          </div>
          <div className="amount-row">
            <span className="amount-label">未回款</span>
            <span className="amount-value unpaid">{data.unRepaymentAmount}元</span>
          </div>
        </div>
      </div>

      <div className="payment-plan">
        <h2 className="plan-title">计划回款日期</h2>
        <ul className="plan-list">
          {data.planList && data.planList.map((item: any, index: number) => (
            <li className="plan-item" key={index}>
              <span className="task-name">{item.taskName}</span>
              <span className="task-date">{item.repaymentDate}</span>
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
};

export default ProjectRepayment;