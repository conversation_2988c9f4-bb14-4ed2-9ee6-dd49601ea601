// zdhj-prjboard-web/app/components/ProjectOverview.tsx
"use client";

import React, { useEffect, useState } from 'react';
import { getProjectInfo } from '@/services/api';

interface ProjectOverviewProps {
  projectId: string;
}

const ProjectOverview: React.FC<ProjectOverviewProps> = ({ projectId }) => {
  const [data, setData] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (projectId) {
      getProjectInfo(projectId).then(response => {
        // 转换后端中文字段名为前端期望的英文字段名
        const transformedData = {
          prjName: response["项目名称"] || "未知项目",
          prjInfo: response["项目概况"] || "暂无项目概况",
          prjStatus: response["项目状态"] || "未知项目", // 后端没有返回状态，设置默认值
          entrustUnit: response["委托方"] || "",
          capitalSourceName: response["资金来源"] || "",
          investedAmount: response["投资金额万元"] || 0,
          prjBldArea: response["建筑面积"] || 0,
          isWholeProcess: response["全过程咨询"] || "",
          bizTypeName: response["二级业务类型"] || response["一级业务类型"] || "",
          firstResourcer: response["第一资源人"] || ""
        };
        setData(transformedData);
        setLoading(false);
      });
    }
  }, [projectId]);

  if (loading) return <div className="project-card">加载中...</div>;
  if (!data) return <div className="project-card">无法加载项目概况</div>;

  return (
    <div className="project-card">
      <div className="project-header">
        <img src="/img/xmtp.png" alt="Project Thumbnail" />
        <div className="project-title text-2xl font-bold">{data.prjName}</div>
        <span className="status-badge">{data.prjStatus}</span>
      </div>

      <div className="project-content">
        <img src="/img/icon.png" alt="Icon" />
        <h3 className="project-section-title">项目概况</h3>

        <div className="mb-4">
          <p className="text-gray-700">{data.prjInfo}</p>
        </div>

        <div className="info-list">
          <div className="info-item">
            <span className="info-label">资金来源:</span>
            <span className="info-value">{data.capitalSourceName}</span>
          </div>
          <div className="info-item">
            <span className="info-label">委托方:</span>
            <span className="info-value">{data.entrustUnit}</span>
          </div>
          <div className="info-item">
            <span className="info-label">投资金额(万元):</span>
            <span className="info-value">{data.investedAmount}</span>
          </div>
          <div className="info-item">
            <span className="info-label">建筑面积(㎡):</span>
            <span className="info-value">{data.prjBldArea}</span>
          </div>
          <div className="info-item">
            <span className="info-label">全过程工程咨询:</span>
            <span className="info-value">{data.isWholeProcess}</span>
          </div>
          <div className="info-item">
            <span className="info-label">业务类型:</span>
            <span className="info-value">{data.bizTypeName}</span>
          </div>
          <div className="info-item">
            <span className="info-label">第一资源人:</span>
            <span className="info-value">{data.firstResourcer}</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProjectOverview;