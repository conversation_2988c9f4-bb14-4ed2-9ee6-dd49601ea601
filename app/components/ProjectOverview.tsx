// zdhj-prjboard-web/app/components/ProjectOverview.tsx
"use client";

import React, { useEffect, useState } from 'react';
import { getProjectInfo } from '@/services/api';
import { Building2, MapPin, DollarSign, Users, FileText, Target, Crown } from 'lucide-react';

interface ProjectOverviewProps {
  projectId: string;
}

const ProjectOverview: React.FC<ProjectOverviewProps> = ({ projectId }) => {
  const [data, setData] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (projectId) {
      getProjectInfo(projectId).then(response => {
        // 转换后端中文字段名为前端期望的英文字段名
        const transformedData = {
          prjName: response["项目概况"] || "未知项目",
          prjStatus: "进行中", // 后端没有返回状态，设置默认值
          entrustUnit: response["委托方"] || "",
          capitalSourceName: response["资金来源"] || "",
          investedAmount: response["投资金额万元"] || 0,
          prjBldArea: response["建筑面积"] || 0,
          isWholeProcess: response["全过程咨询"] || "",
          bizTypeName: response["二级业务类型"] || response["一级业务类型"] || "",
          firstResourcer: response["第一资源人"] || ""
        };
        setData(transformedData);
        setLoading(false);
      });
    }
  }, [projectId]);

  if (loading) return <div className="project-card">加载中...</div>;
  if (!data) return <div className="project-card">无法加载项目概况</div>;

  return (
    <div className="project-card">
      <div className="project-header">
        <img src="/img/xmtp.png" alt="Project Thumbnail" />
        <div className="project-title">{data.prjName}</div>
        <span className="status-badge">{data.prjStatus}</span>
      </div>

      <div className="project-content">
        <img src="/img/icon.png" alt="Icon" />
        <h3 className="project-section-title">项目概况</h3>

        
        <div className="info-list">
          <div className="info-item group hover:bg-gray-50 transition-colors duration-200 rounded-md p-1">
            <span className="info-label flex items-center gap-1">
              <DollarSign className="w-3 h-3 text-green-600" />
              资金来源:
            </span>
            <span className="info-value">{data.capitalSourceName}</span>
          </div>
          <div className="info-item group hover:bg-gray-50 transition-colors duration-200 rounded-md p-1">
            <span className="info-label flex items-center gap-1">
              <Building2 className="w-3 h-3 text-blue-600" />
              委托方:
            </span>
            <span className="info-value">{data.entrustUnit}</span>
          </div>
          <div className="info-item group hover:bg-gray-50 transition-colors duration-200 rounded-md p-1">
            <span className="info-label flex items-center gap-1">
              <Target className="w-3 h-3 text-purple-600" />
              投资金额(万元):
            </span>
            <span className="info-value font-semibold text-purple-700">{data.investedAmount}</span>
          </div>
          <div className="info-item group hover:bg-gray-50 transition-colors duration-200 rounded-md p-1">
            <span className="info-label flex items-center gap-1">
              <MapPin className="w-3 h-3 text-orange-600" />
              建筑面积(㎡):
            </span>
            <span className="info-value font-semibold text-orange-700">{data.prjBldArea}</span>
          </div>
          <div className="info-item group hover:bg-gray-50 transition-colors duration-200 rounded-md p-1">
            <span className="info-label flex items-center gap-1">
              <FileText className="w-3 h-3 text-teal-600" />
              全过程工程咨询:
            </span>
            <span className="info-value">{data.isWholeProcess}</span>
          </div>
          <div className="info-item group hover:bg-gray-50 transition-colors duration-200 rounded-md p-1">
            <span className="info-label flex items-center gap-1">
              <Users className="w-3 h-3 text-indigo-600" />
              业务类型:
            </span>
            <span className="info-value">{data.bizTypeName}</span>
          </div>
          <div className="info-item group hover:bg-gray-50 transition-colors duration-200 rounded-md p-1">
            <span className="info-label flex items-center gap-1">
              <Crown className="w-3 h-3 text-amber-600" />
              第一资源人:
            </span>
            <span className="info-value font-semibold text-amber-700">{data.firstResourcer}</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProjectOverview;