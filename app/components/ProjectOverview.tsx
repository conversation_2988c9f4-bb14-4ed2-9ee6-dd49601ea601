// zdhj-prjboard-web/app/components/ProjectOverview.tsx
"use client";

import React, { useEffect, useState } from 'react';
import { getProjectInfo } from '@/services/api';

interface ProjectOverviewProps {
  projectId: string;
}

const ProjectOverview: React.FC<ProjectOverviewProps> = ({ projectId }) => {
  const [data, setData] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (projectId) {
      getProjectInfo(projectId).then(response => {
        setData(response.data);
        setLoading(false);
      });
    }
  }, [projectId]);

  if (loading) return <div className="project-card">加载中...</div>;
  if (!data) return <div className="project-card">无法加载项目概况</div>;

  return (
    <div className="project-card">
      <div className="project-header">
        <img src="/img/xmtp.png" alt="Project Thumbnail" />
        <div className="project-title">{data.prjName}</div>
        <span className="status-badge">{data.prjStatus}</span>
      </div>

      <div className="project-content">
        <img src="/img/icon.png" alt="Icon" />
        <h3 className="project-section-title">项目概况</h3>

        <div className="company-name">{data.entrustUnit}</div>

        <div className="info-list">
          <div className="info-item">
            <span className="info-label">资金来源:</span>
            <span className="info-value">{data.capitalSourceName}</span>
          </div>
          <div className="info-item">
            <span className="info-label">委托方:</span>
            <span className="info-value">{data.entrustUnit}</span>
          </div>
          <div className="info-item">
            <span className="info-label">投资金额(万元):</span>
            <span className="info-value">{data.investedAmount}</span>
          </div>
          <div className="info-item">
            <span className="info-label">建筑面积(㎡):</span>
            <span className="info-value">{data.prjBldArea}</span>
          </div>
          <div className="info-item">
            <span className="info-label">全过程工程咨询:</span>
            <span className="info-value">{data.isWholeProcess}</span>
          </div>
          <div className="info-item">
            <span className="info-label">业务类型:</span>
            <span className="info-value">{data.bizTypeName}</span>
          </div>
          <div className="info-item">
            <span className="info-label">第一资源人:</span>
            <span className="info-value">{data.firstResourcer}</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProjectOverview;