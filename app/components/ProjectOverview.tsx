// zdhj-prjboard-web/app/components/ProjectOverview.tsx
"use client";

import React, { useEffect, useState } from 'react';
import { getProjectInfo } from '@/services/api';

interface ProjectOverviewProps {
  projectId: string;
}

const ProjectOverview: React.FC<ProjectOverviewProps> = ({ projectId }) => {
  const [data, setData] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (projectId) {
      getProjectInfo(projectId).then(response => {
        // 转换后端中文字段名为前端期望的英文字段名
        const transformedData = {
          prjName: response["项目名称"] || "未知项目",
          prjStatus: response["项目状态"] || "状态未知", // 后端没有返回状态，设置默认值
          prjInfo: response["项目概况"] || "项目概况未知",
          entrustUnit: response["委托方"] || "委托方未知",
          capitalSourceName: response["资金来源"] || "资金来源未知",
          investedAmount: response["投资金额万元"] || 0,
          prjBldArea: response["建筑面积"] || 0,
          isWholeProcess: response["全过程咨询"] || "",
          bizTypeName: response["二级业务类型"] || response["一级业务类型"] || "",
          firstResourcer: response["第一资源人"] || ""
        };
        setData(transformedData);
        setLoading(false);
      });
    }
  }, [projectId]);

  if (loading) return <div className="project-card">加载中...</div>;
  if (!data) return <div className="project-card">无法加载项目概况</div>;

  return (
    <div 
      className="module"

    >
      <div className="header"
           style={{
             backgroundImage: "url('/img/lan.png')",
             backgroundRepeat: 'no-repeat',
             backgroundSize: '100% 100%'
           }}

      >


      <div className="project-header p-4 w-full text-white">
        <img src="/img/xmtp.png" alt="Project Thumbnail" className="w-full float-none mr-0" />
        <div className="project-title">{data.prjName}</div>
        <div className="project-info">{data.prjInfo}</div>
        <span className="status-badge bg-[#52c41a] text-white py-1 px-3 rounded-[20px] text-[12px] font-medium inline-block">{data.prjStatus}</span>
      </div>
      </div>
      <div className="project-content">
        <img src="/img/icon.png" alt="Icon" />
        <h3 className="project-section-title">基本信息</h3>

        <div className="info-list">
          <div className="info-item">
            <span className="info-label">资金来源:</span>
            <span className="info-value">{data.capitalSourceName}</span>
          </div>
          <div className="info-item">
            <span className="info-label  " >委托方:</span>
            <span className="info-value">{data.entrustUnit}</span>
          </div>
          <div className="info-item">
            <span className="info-label">投资金额(万元):</span>
            <span className="info-value">{data.investedAmount}</span>
          </div>
          <div className="info-item">
            <span className="info-label">建筑面积(㎡):</span>
            <span className="info-value">{data.prjBldArea}</span>
          </div>
          <div className="info-item">
            <span className="info-label">全过程工程咨询:</span>
            <span className="info-value">{data.isWholeProcess}</span>
          </div>
          <div className="info-item">
            <span className="info-label">业务类型:</span>
            <span className="info-value">{data.bizTypeName}</span>
          </div>
          <div className="info-item">
            <span className="info-label">第一资源人:</span>
            <span className="info-value">{data.firstResourcer}</span>
          </div>
        </div>
      </div>

        </div>
  );
};

export default ProjectOverview;