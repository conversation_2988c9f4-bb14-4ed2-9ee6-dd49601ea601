// zdhj-prjboard-web/app/components/ProjectOverview.tsx
"use client";

import React, { useEffect, useState } from 'react';
import { getProjectInfo } from '@/services/api';
import { 
  Wallet, 
  Building2, 
  User, 
  FileText, 
  Users, 
  Badge,
  percent,
  Tag
} from 'lucide-react';

interface ProjectOverviewProps {
  projectId: string;
}

const ProjectOverview: React.FC<ProjectOverviewProps> = ({ projectId }) => {
  const [data, setData] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (projectId) {
      getProjectInfo(projectId).then(response => {
        // 转换后端中文字段名为前端期望的英文字段名
        const transformedData = {
          prjName: response["项目名称"] || "未知项目",
          prjStatus: response["项目状态"] || "状态未知", // 后端没有返回状态，设置默认值
          prjInfo: response["项目概况"] || "项目概况未知",
          entrustUnit: response["委托方"] || "委托方未知",
          capitalSourceName: response["资金来源"] || "资金来源未知",
          investedAmount: response["投资金额万元"] || 0,
          prjBldArea: response["建筑面积"] || 0,
          isWholeProcess: response["全过程咨询"] || "",
          bizTypeName: response["二级业务类型"] || response["一级业务类型"] || "",
          firstResourcer: response["第一资源人"] || ""
        };
        setData(transformedData);
        setLoading(false);
      });
    }
  }, [projectId]);

  if (loading) return <div className="project-card">加载中...</div>;
  if (!data) return <div className="project-card">无法加载项目概况</div>;

  return (
      <div className="module">
        <div className="module-header">
          <h1 className="module-title">项目概况</h1>
        </div>

      <div 
        className="project-header text-white p-4 w-full"
        style={{ 
          backgroundImage: "url('/img/lan.png')",
          backgroundRepeat: 'no-repeat',
          backgroundSize: '100% 100%'
        }}
      >
        <img src="/img/xmtp.png" alt="Project Thumbnail" className="w-full float-none mr-0" />
        <div className="project-title">{data.prjName}</div>
        <div className="project-info">{data.prjInfo}</div>
        <span className="status-badge bg-[#52c41a] text-white py-1 px-3 rounded-[20px] text-[12px] font-medium inline-block">{data.prjStatus}</span>
      </div>

      <div className="project-content">
        <img src="/img/icon.png" alt="Icon" />
        <h3 className="project-section-title">基本信息</h3>

        <div className="info-list">
          <div className="info-item flex items-center gap-2">
            <Wallet className="info-icon text-blue-500 flex-shrink-0" size={16} />
            <span className="info-label flex-shrink-0">资金来源:</span>
            <span className="info-value flex-grow">{data.capitalSourceName}</span>
          </div>
          <div className="info-item flex items-center gap-2">
            <Building2 className="info-icon text-blue-500 flex-shrink-0" size={16} />
            <span className="info-label flex-shrink-0">委托方:</span>
            <span className="info-value flex-grow">{data.entrustUnit}</span>
          </div>
          <div className="info-item flex items-center gap-2">
            <Wallet className="info-icon text-blue-500 flex-shrink-0" size={16} />
            <span className="info-label flex-shrink-0">投资金额(万元):</span>
            <span className="info-value flex-grow font-medium">{data.investedAmount}</span>
          </div>
          <div className="info-item flex items-center gap-2">
            <Building2 className="info-icon text-blue-500 flex-shrink-0" size={16} />
            <span className="info-label flex-shrink-0">建筑面积(㎡):</span>
            <span className="info-value flex-grow">{data.prjBldArea}</span>
          </div>
          <div className="info-item flex items-center gap-2">
            <FileText className="info-icon text-blue-500 flex-shrink-0" size={16} />
            <span className="info-label flex-shrink-0">全过程工程咨询:</span>
            <span className="info-value flex-grow">{data.isWholeProcess}</span>
          </div>
          <div className="info-item flex items-center gap-2">
            <Tag className="info-icon text-blue-500 flex-shrink-0" size={16} />
            <span className="info-label flex-shrink-0">业务类型:</span>
            <span className="info-value flex-grow">{data.bizTypeName}</span>
          </div>
          <div className="info-item flex items-center gap-2">
            <User className="info-icon text-blue-500 flex-shrink-0" size={16} />
            <span className="info-label flex-shrink-0">第一资源人:</span>
            <span className="info-value flex-grow">{data.firstResourcer}</span>
          </div>
        </div>
      </div>

        </div>
  );
};

export default ProjectOverview;