// zdhj-prjboard-web/app/components/ProjectOverview.tsx
"use client";

import React, { useEffect, useState } from 'react';
import { getProjectInfo } from '@/services/api';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Building2, MapPin, DollarSign, Users, FileText, Target, Crown } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ProjectOverviewProps {
  projectId: string;
}

const ProjectOverview: React.FC<ProjectOverviewProps> = ({ projectId }) => {
  const [data, setData] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (projectId) {
      getProjectInfo(projectId).then(response => {
        // 转换后端中文字段名为前端期望的英文字段名
        const transformedData = {
          prjName: response["项目概况"] || "未知项目",
          prjStatus: "进行中", // 后端没有返回状态，设置默认值
          entrustUnit: response["委托方"] || "",
          capitalSourceName: response["资金来源"] || "",
          investedAmount: response["投资金额万元"] || 0,
          prjBldArea: response["建筑面积"] || 0,
          isWholeProcess: response["全过程咨询"] || "",
          bizTypeName: response["二级业务类型"] || response["一级业务类型"] || "",
          firstResourcer: response["第一资源人"] || ""
        };
        setData(transformedData);
        setLoading(false);
      });
    }
  }, [projectId]);

  if (loading) {
    return (
      <Card className="h-full">
        <CardContent className="flex items-center justify-center h-full">
          <div className="flex items-center space-x-2">
            <div className="w-4 h-4 bg-blue-500 rounded-full animate-pulse"></div>
            <span className="text-sm text-muted-foreground">加载中...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!data) {
    return (
      <Card className="h-full">
        <CardContent className="flex items-center justify-center h-full">
          <span className="text-sm text-muted-foreground">无法加载项目概况</span>
        </CardContent>
      </Card>
    );
  }

  const infoItems = [
    { icon: DollarSign, label: "资金来源", value: data.capitalSourceName, color: "text-green-600" },
    { icon: Building2, label: "委托方", value: data.entrustUnit, color: "text-blue-600" },
    { icon: Target, label: "投资金额(万元)", value: data.investedAmount, color: "text-purple-600" },
    { icon: MapPin, label: "建筑面积(㎡)", value: data.prjBldArea, color: "text-orange-600" },
    { icon: FileText, label: "全过程工程咨询", value: data.isWholeProcess, color: "text-teal-600" },
    { icon: Users, label: "业务类型", value: data.bizTypeName, color: "text-indigo-600" },
    { icon: Crown, label: "第一资源人", value: data.firstResourcer, color: "text-amber-600" },
  ];

  return (
    <Card className="h-full overflow-hidden bg-gradient-to-br from-blue-50 via-white to-purple-50 border-0 shadow-lg">
      {/* Header with background image */}
      <CardHeader
        className="relative p-4 text-white bg-cover bg-center bg-no-repeat min-h-[120px]"
        style={{ backgroundImage: 'url(/img/lan.png)' }}
      >
        <div className="flex items-start space-x-3">
          <div className="flex-shrink-0">
            <img
              src="/img/xmtp.png"
              alt="Project Thumbnail"
              className="w-16 h-16 rounded-lg object-cover border-2 border-white/20 shadow-lg"
            />
          </div>
          <div className="flex-1 min-w-0">
            <h2 className="text-lg font-bold leading-tight mb-2 text-white drop-shadow-sm">
              {data.prjName}
            </h2>
            <Badge
              variant="secondary"
              className="bg-green-500 hover:bg-green-600 text-white border-0 shadow-sm"
            >
              {data.prjStatus}
            </Badge>
          </div>
        </div>
      </CardHeader>

      {/* Content */}
      <CardContent className="p-4 flex-1 overflow-y-auto">
        <div className="flex items-center space-x-2 mb-4">
          <img src="/img/icon.png" alt="Icon" className="w-5 h-5" />
          <h3 className="text-base font-semibold text-gray-800">项目概况</h3>
        </div>

        <div className="mb-4 p-3 bg-gray-50 rounded-lg border">
          <p className="text-sm text-gray-600 leading-relaxed">{data.entrustUnit}</p>
        </div>

        <div className="space-y-3">
          {infoItems.map((item, index) => (
            <div key={index} className="flex items-center justify-between p-2 rounded-lg hover:bg-gray-50 transition-colors">
              <div className="flex items-center space-x-2 flex-1 min-w-0">
                <item.icon className={cn("w-4 h-4 flex-shrink-0", item.color)} />
                <span className="text-xs font-medium text-gray-600 truncate">
                  {item.label}:
                </span>
              </div>
              <span className="text-xs font-semibold text-gray-900 text-right max-w-[120px] truncate">
                {item.value || '暂无'}
              </span>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default ProjectOverview;