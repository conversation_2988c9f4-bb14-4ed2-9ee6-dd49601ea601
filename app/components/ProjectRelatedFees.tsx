// zdhj-prjboard-web/app/components/ProjectRelatedFees.tsx
"use client";

import React, { useEffect, useRef, useState } from 'react';
import { getRelatedFees } from '@/services/api';

interface ProjectRelatedFeesProps {
  projectId: string;
}

const ProjectRelatedFees: React.FC<ProjectRelatedFeesProps> = ({ projectId }) => {
  const chartRef = useRef<HTMLCanvasElement>(null);
  const [data, setData] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (projectId) {
      getRelatedFees(projectId).then(response => {
        setData(response.data);
        setLoading(false);
      });
    }
  }, [projectId]);

  useEffect(() => {
    if (data && chartRef.current && (window as any).Chart) {
      const ctx = chartRef.current.getContext('2d');
      if (ctx) {
        let chartStatus = (window as any).Chart.getChart(chartRef.current);
        if (chartStatus != undefined) {
          chartStatus.destroy();
        }

        const { Chart } = (window as any);
        const feeLabels = data.feeDetails.map((fee: any) => fee.feeName);
        const feeValues = data.feeDetails.map((fee: any) => fee.amount);

        new Chart(ctx, {
          type: 'doughnut',
          data: {
            labels: feeLabels,
            datasets: [{
              data: feeValues,
              backgroundColor: ['#4075ef', '#33c2fa', '#05c350', '#fe7e2e'],
              borderWidth: 0,
              hoverOffset: 15
            }]
          },
          options: {
            cutout: '65%',
            plugins: {
              legend: {
                display: false
              },
              tooltip: {
                callbacks: {
                  label: function(context: any) {
                    const value = context.raw;
                    const total = context.dataset.data.reduce((a: number, b: number) => a + b, 0);
                    const percentage = total > 0 ? Math.round((value / total) * 100) : 0;
                    return `${context.label}: ${value}元 (${percentage}%)`;
                  }
                }
              }
            }
          }
        });
      }
    }
  }, [data]);

  if (loading) return <div className="column column-2">加载中...</div>;
  if (!data) return <div className="column column-2">无法加载相关费用信息</div>;

  const feeIconMap: { [key: string]: string } = {
    '执业费': '/img/zyf.png',
    '市场费': '/img/SCF.png',
    '差旅费': '/img/CLF.png',
    '专家出场费': '/img/ZJCCF.png',
  };

  return (
    <div className="column column-2">
      <div className="expense-header">
        <h1>项目相关费用</h1>
      </div>

      <div className="expense-content">
        <div className="expense-chart-container">
          <div className="expense-chart-wrapper">
            <canvas ref={chartRef} id="expensePieChart"></canvas>
            <div className="expense-chart-center">
              <div className="expense-total-amount">{data.totalFees}</div>
              <div className="expense-total-label">总费用</div>
            </div>
          </div>
        </div>

        <div className="expense-details-container">
          <div className="expense-grid">
            {data.feeDetails && data.feeDetails.map((fee: any, index: number) => (
              <div className="expense-detail-item" key={index}>
                <div className="expense-detail-icon">
                  <img src={feeIconMap[fee.feeName] || '/img/icon.png'} alt={fee.feeName} />
                </div>
                <div className="expense-detail-info">
                  <div className="expense-detail-name">{fee.feeName}</div>
                  <div className="expense-detail-amount">{fee.amount}元</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProjectRelatedFees;
