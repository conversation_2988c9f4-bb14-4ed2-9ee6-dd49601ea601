'use client';

import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Activity, Briefcase, DollarSign, FileText, Users, Paperclip } from 'lucide-react';

// Importing the original components
import ProjectInfo from '@/components/ProjectInfo';
import ProjectRepayment from '@/components/ProjectRepayment';
import ProjectDigitalAssets from '@/components/ProjectDigitalAssets';
import ProjectWorkflows from '@/components/ProjectWorkflows';
import ProjectTaskAssignment from '@/components/ProjectTaskAssignment';
import ProjectRelatedFees from '@/components/ProjectRelatedFees';
import ProjectProgress from '@/components/ProjectProgress';

// A new component for stat cards, now with flex-grow capability
const StatCard = ({ title, icon, children }: { title: string, icon: React.ReactNode, children?: React.ReactNode }) => (
    <Card className="h-full flex flex-col">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{title}</CardTitle>
            {icon}
        </CardHeader>
        <CardContent className="flex-grow flex flex-col">
            {children}
        </CardContent>
    </Card>
);


export default function Dashboard() {
    const projectId = '2369'; // Hardcoded project ID

    return (
        <div className="flex min-h-screen w-full flex-col bg-muted/40">
            <main className="flex flex-1 flex-col gap-4 p-4 md:gap-8 md:p-8">
                {/* Top Row: Project Info and Key Stats with uniform height */}
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                    <Card className="lg:col-span-2">
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Briefcase className="h-6 w-6" />
                                 项目基本信息
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="p-4">
                           <ProjectInfo projectId={projectId} />
                        </CardContent>
                    </Card>
                     <StatCard title=" 项目总进度" icon={<Activity className="h-4 w-4 text-muted-foreground" />}>
                        <ProjectProgress projectId={projectId} />
                     </StatCard>
                     <StatCard title=" 项目任务分配" icon={<Users className="h-4 w-4 text-muted-foreground" />}>
                        <ProjectTaskAssignment projectId={projectId} />
                     </StatCard>
                </div>

                {/* Main Content: Two-column layout */}
                <div className="grid gap-4 md:gap-8 lg:grid-cols-3">
                    {/* Left Column */}
                    <div className="lg:col-span-2 grid auto-rows-max items-start gap-4 md:gap-8">
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <FileText className="h-6 w-6" />
                                     项目相关流程
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="p-4 max-h-96 overflow-y-auto">
                                <ProjectWorkflows projectId={projectId} />
                            </CardContent>
                        </Card>
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Paperclip className="h-6 w-6" />
                                     项目数字资产
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="p-4 max-h-96 overflow-y-auto">
                                <ProjectDigitalAssets projectId={projectId} />
                            </CardContent>
                        </Card>
                    </div>

                    {/* Right Column */}
                    <div className="grid auto-rows-max items-start gap-4 md:gap-8">
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                     <DollarSign className="h-6 w-6" />
                                     财务状况
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <Tabs defaultValue="repayment">
                                    <TabsList className="grid w-full grid-cols-2">
                                        <TabsTrigger value="repayment"> 回款计划</TabsTrigger>
                                        <TabsTrigger value="fees"> 相关费用</TabsTrigger>
                                    </TabsList>
                                    <TabsContent value="repayment" className="p-4 max-h-80 overflow-y-auto">
                                        <ProjectRepayment projectId={projectId} />
                                    </TabsContent>
                                    <TabsContent value="fees" className="p-4 max-h-80 overflow-y-auto">
                                        <ProjectRelatedFees projectId={projectId} />
                                    </TabsContent>
                                </Tabs>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </main>
        </div>
    );
}
