import type { Metada<PERSON> } from "next";
import Script from "next/script";
import "./globals.css";
import "./style.css";

export const metadata: Metadata = {
  title: "中德华建集团 - 项目看板",
  description: "中德华建集团 - 项目看板",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="zh-CN">
      <head>
        <link
          rel="stylesheet"
          href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
        />
      </head>
      <body suppressHydrationWarning>
        {children}
        <Script src="https://cdn.jsdelivr.net/npm/chart.js" />
      </body>
    </html>
  );
}