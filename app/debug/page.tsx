"use client";

import React, { useEffect, useState } from 'react';
import { getProjectInfo, getDigitalAssets } from '@/services/api';

export default function DebugPage() {
  const [projectInfoResult, setProjectInfoResult] = useState<any>(null);
  const [milestonesResult, setMilestonesResult] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [errors, setErrors] = useState<any>({});

  useEffect(() => {
    const testAPIs = async () => {
      const projectId = "1"; // 使用测试项目ID
      
      // 测试项目概况API
      try {
        console.log("Testing getProjectInfo...");
        const projectInfo = await getProjectInfo(projectId);
        console.log("Project Info Response:", projectInfo);
        setProjectInfoResult(projectInfo);
      } catch (error) {
        console.error("Project Info Error:", error);
        setErrors(prev => ({ ...prev, projectInfo: error }));
      }

      // 测试里程碑API
      try {
        console.log("Testing getDigitalAssets...");
        const milestones = await getDigitalAssets(projectId);
        console.log("Milestones Response:", milestones);
        setMilestonesResult(milestones);
      } catch (error) {
        console.error("Milestones Error:", error);
        setErrors(prev => ({ ...prev, milestones: error }));
      }

      setLoading(false);
    };

    testAPIs();
  }, []);

  if (loading) {
    return <div className="p-8">正在测试API...</div>;
  }

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">API 调试页面</h1>
      
      <div className="space-y-6">
        {/* 项目概况API测试结果 */}
        <div className="border rounded-lg p-4">
          <h2 className="text-lg font-semibold mb-3">项目概况 API (getPrjInfo/1)</h2>
          {errors.projectInfo ? (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
              <strong>错误:</strong> {errors.projectInfo.message || JSON.stringify(errors.projectInfo)}
            </div>
          ) : (
            <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
              <strong>成功:</strong>
              <pre className="mt-2 text-sm overflow-auto">
                {JSON.stringify(projectInfoResult, null, 2)}
              </pre>
            </div>
          )}
        </div>

        {/* 里程碑API测试结果 */}
        <div className="border rounded-lg p-4">
          <h2 className="text-lg font-semibold mb-3">里程碑 API (attachments/1)</h2>
          {errors.milestones ? (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
              <strong>错误:</strong> {errors.milestones.message || JSON.stringify(errors.milestones)}
            </div>
          ) : (
            <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
              <strong>成功:</strong>
              <pre className="mt-2 text-sm overflow-auto">
                {JSON.stringify(milestonesResult, null, 2)}
              </pre>
            </div>
          )}
        </div>

        {/* API配置信息 */}
        <div className="border rounded-lg p-4">
          <h2 className="text-lg font-semibold mb-3">API 配置信息</h2>
          <div className="space-y-2 text-sm">
            <p><strong>USE_PROXY:</strong> {process.env.NEXT_PUBLIC_USE_PROXY}</p>
            <p><strong>API_BASE_URL:</strong> {process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8080/api/project'}</p>
          </div>
        </div>

        {/* 手动测试按钮 */}
        <div className="border rounded-lg p-4">
          <h2 className="text-lg font-semibold mb-3">手动测试</h2>
          <div className="space-x-4">
            <button 
              onClick={() => window.location.reload()}
              className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
            >
              重新测试
            </button>
            <button 
              onClick={() => {
                fetch('/api/proxy?endpoint=getPrjInfo/1')
                  .then(res => res.json())
                  .then(data => console.log('Direct proxy test:', data))
                  .catch(err => console.error('Direct proxy error:', err));
              }}
              className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600"
            >
              直接测试代理
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
