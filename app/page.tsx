import ProjectOverview from './components/ProjectOverview';
import ProjectRepayment from './components/ProjectRepayment';
import ProjectTaskAssignment from './components/ProjectTaskAssignment';
import ProjectMilestones from './components/ProjectMilestones';
import ProjectWorkflows from './components/ProjectWorkflows';
import ProjectRelatedFees from './components/ProjectRelatedFees';
import ProjectProgress from './components/ProjectProgress';

export default function Home() {
  return (
    <>
      <div className="nav-container">
        <nav className="navbar">
          {/* Logo区域 */}
          <div className="logo">
            <img src="/img/logo.png" alt="Logo" />
          </div>

          {/* 导航菜单 */}
          <ul className="nav-menu">
            <li className="nav-item active">
              <a href="#">项目概括</a>
            </li>
            <li className="nav-item">
              <a href="#">项目进度</a>
            </li>
            <li className="nav-item">
              <a href="#">数字资产</a>
            </li>
            <li className="nav-item">
              <a href="#">成本费用</a>
            </li>
            <li className="nav-item">
              <a href="#">任务安排</a>
            </li>
            <li className="nav-item">
              <a href="#">开票回款</a>
            </li>
            <li className="nav-item">
              <a href="#">相关流程</a>
            </li>
            <li className="nav-item">
              <a href="#">提奖分配</a>
            </li>
            <li className="nav-item">
              <a href="#">里程碑</a>
            </li>
          </ul>

          {/* 搜索区域 */}
          <div className="search-box">
            <input type="text" className="search-input" placeholder="输入关键字" />
            <span className="search-icon">🔍</span>
          </div>

          {/* 用户按钮 */}
          <button className="user-btn">名字</button>
        </nav>
      </div>

      <div className="bianju">
        <div className="top-row">
          <ProjectOverview projectId="2369" />
          <ProjectRepayment projectId="2369" />
          <ProjectTaskAssignment projectId="2369" />
          <ProjectMilestones projectId="2369" />
        </div>

        <div className="three-column-layout">
          <ProjectWorkflows projectId="2369" />
          <ProjectRelatedFees projectId="2369" />
          <ProjectProgress projectId="2369" />
        </div>
      </div>
    </>
  );
}