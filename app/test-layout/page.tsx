export default function TestLayout() {
  return (
    <div className="main-container">
      {/* 导航栏 - 8vh */}
      <div className="nav-container">
        <nav className="navbar">
          <div className="logo">
            <img src="/img/logo.png" alt="Logo" />
          </div>
          
          <ul className="nav-menu">
            <li className="nav-item active"><a href="#">项目概括</a></li>
            <li className="nav-item"><a href="#">项目进度</a></li>
            <li className="nav-item"><a href="#">数字资产</a></li>
            <li className="nav-item"><a href="#">成本费用</a></li>
            <li className="nav-item"><a href="#">任务安排</a></li>
            <li className="nav-item"><a href="#">开票回款</a></li>
            <li className="nav-item"><a href="#">相关流程</a></li>
            <li className="nav-item"><a href="#">提奖分配</a></li>
            <li className="nav-item"><a href="#">里程碑</a></li>
          </ul>
          
          <div className="search-box">
            <input type="text" className="search-input" placeholder="输入关键字" />
            <span className="search-icon">🔍</span>
          </div>
          
          <button className="user-btn">名字</button>
        </nav>
      </div>

      {/* 主内容区域 - 92vh */}
      <div className="main-content">
        {/* 上方四个板块 - 35vh */}
        <div className="top-row">
          <div className="project-card">
            <div className="project-header">
              <img src="/img/xmtp.png" alt="项目图片" />
              <div className="project-title">测试项目标题</div>
              <span className="status-badge">进行中</span>
            </div>
            <div className="project-content">
              <h3 className="project-section-title">项目概况</h3>
              <div className="info-list">
                {Array.from({length: 10}, (_, i) => (
                  <div className="info-item" key={i}>
                    <span className="info-label">测试项目 {i + 1}:</span>
                    <span className="info-value">测试数值 {i + 1}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>

          <div className="module">
            <div className="module-title">测试模块 2</div>
            <div className="module-content">
              <div style={{height: '200px', background: '#f0f0f0', display: 'flex', alignItems: 'center', justifyContent: 'center'}}>
                测试内容区域
              </div>
            </div>
          </div>

          <div className="module">
            <div className="module-title">测试模块 3</div>
            <div className="module-content">
              <div style={{height: '200px', background: '#f0f0f0', display: 'flex', alignItems: 'center', justifyContent: 'center'}}>
                测试内容区域
              </div>
            </div>
          </div>

          <div className="module">
            <div className="module-title">测试模块 4</div>
            <div className="module-content">
              <div style={{height: '200px', background: '#f0f0f0', display: 'flex', alignItems: 'center', justifyContent: 'center'}}>
                测试内容区域
              </div>
            </div>
          </div>
        </div>

        {/* 下方三个板块 - 54vh */}
        <div className="bottom-row">
          <div className="three-column-layout">
            <div className="column column-1">
              <div className="header">
                <h1>测试列 1</h1>
              </div>
              <div className="timeline">
                {Array.from({length: 20}, (_, i) => (
                  <div className="timeline-node" key={i}>
                    <div className="node-header">
                      <div className="node-title">测试节点 {i + 1}</div>
                      <div className="node-date">2024-01-{String(i + 1).padStart(2, '0')}</div>
                    </div>
                    <div className="file-card">
                      <div className="file-title">测试文件 {i + 1}</div>
                      <div className="file-content">这是测试内容 {i + 1}</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="column column-2">
              <div className="expense-header">
                <h1>测试列 2</h1>
              </div>
              <div className="expense-content">
                <div className="expense-chart-container">
                  <div style={{width: '150px', height: '150px', background: '#e0e0e0', borderRadius: '50%', display: 'flex', alignItems: 'center', justifyContent: 'center'}}>
                    图表区域
                  </div>
                </div>
                <div className="expense-details-container">
                  <div className="expense-grid">
                    {Array.from({length: 12}, (_, i) => (
                      <div className="expense-detail-item" key={i}>
                        <div className="expense-detail-name">费用项 {i + 1}</div>
                        <div className="expense-detail-amount">{(i + 1) * 1000}元</div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            <div className="column column-3">
              <div className="chart-header">
                <h1>测试列 3</h1>
                <div className="chart-legend">
                  <div className="legend-item">
                    <div className="legend-color legend-normal"></div>
                    <span>正常</span>
                  </div>
                  <div className="legend-item">
                    <div className="legend-color legend-paused"></div>
                    <span>暂停</span>
                  </div>
                  <div className="legend-item">
                    <div className="legend-color legend-stopped"></div>
                    <span>停止</span>
                  </div>
                  <div className="legend-item">
                    <div className="legend-color legend-total"></div>
                    <span>总计</span>
                  </div>
                </div>
              </div>
              
              <div className="chart-body">
                {Array.from({length: 15}, (_, i) => (
                  <div className="chart-grid" key={i}>
                    <div className="row-label">任务 {i + 1}</div>
                    <div className="progress-container">
                      <div
                        className={`progress-bar ${i % 4 === 0 ? 'bar-total' : i % 3 === 0 ? 'bar-stopped' : i % 2 === 0 ? 'bar-paused' : 'bar-normal'}`}
                        style={{ width: `${Math.random() * 100}%` }}
                      >
                        {Math.floor(Math.random() * 100)}%
                      </div>
                    </div>
                    <div className="percentage-scale">{Math.floor(Math.random() * 100)}%</div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
