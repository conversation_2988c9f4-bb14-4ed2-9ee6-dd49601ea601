* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: "Microsoft YaHei", "Helvetica Neue", Arial, sans-serif;
}

html, body {
    height: 100vh;
    overflow: hidden; /* 禁止页面级滚动 */
    background-color: #f5f6f8;
}

/* 主容器 - 使用100vh确保单屏显示 */
.main-container {
    height: 100vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* 自定义滚动条样式 */
::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
    transition: background 0.3s;
}

::-webkit-scrollbar-thumb:hover {
    background: #a1a1a1;
}

/* Firefox滚动条样式 */
* {
    scrollbar-width: thin;
    scrollbar-color: #c1c1c1 #f1f1f1;
}

.nav-container {
    height: 8vh; /* 导航栏占用8%视口高度 */
    background: #fff;
    border-radius: 4px;
    overflow: hidden;
    flex-shrink: 0; /* 防止导航栏被压缩 */
}

/* 导航栏主体 */
.navbar {
    display: flex;
    align-items: center;
    padding: 0 20px;
    height: 100%;
    background: #fff;
}

/* Logo区域 */
.logo {
    display: flex;
    align-items: center;
    margin-right: 30px;
}

.logo-icon {
    width: 36px;
    height: 36px;
    background: #ffcc00;
    border-radius: 4px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    margin-right: 10px;
}

.logo-text {
    font-size: 16px;
    font-weight: bold;
    color: #000;
    white-space: nowrap;
}

/* 导航菜单 */
.nav-menu {
    display: flex;
    flex: 1;
    list-style: none;
}

.nav-item {
    margin-right: 2px;
}

.nav-item a {
    display: block;
    padding: 0 16px;
    height: 100%;
    line-height: 8vh; /* 与导航栏高度一致 */
    text-decoration: none;
    color: #000;
    font-size: 14px;
    transition: all 0.3s;
}

.nav-item a:hover {
    background: #eaf2fe;
    color: #346cee;
}

.nav-item.active a {
    background: #eaf2fe;
    color: #346cee;
}

/* 搜索区域 */
.search-box {
    position: relative;
    margin-right: 20px;
}

.search-input {
    width: 180px;
    height: 36px;
    padding: 0 36px 0 12px;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    font-size: 14px;
    transition: all 0.3s;
}

.search-input:focus {
    border-color: #1890ff;
    outline: none;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.search-icon {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #999;
    cursor: pointer;
}

/* 用户按钮 */
.user-btn {
    width: 36px;
    height: 36px;
    background: #1890ff;
    color: #fff;
    border: none;
    border-radius: 50%;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s;
}

.user-btn:hover {
    background: #40a9ff;
    transform: scale(1.05);
}

/* 响应式设计 */
@media (max-width: 992px) {
    .nav-menu {
        overflow-x: auto;
        padding-bottom: 5px;
    }
    
    .search-box {
        display: none;
    }
}

/* 主内容区域 - 占用剩余的92%视口高度 */
.main-content {
    height: 92vh; /* 100vh - 8vh(导航栏) */
    padding: 0.5vh 1vw; /* 减少内边距以最大化组件空间 */
    overflow: hidden;
    display: flex;
    flex-direction: column;
    gap: 0.5vh; /* 统一间距 */
}

/* 上方四个板块 - 精确计算高度 */
.top-row {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 0.5vw; /* 减少间距 */
    height: 44vh; /* 增加高度占比 (92vh * 0.48) */
    flex-shrink: 0; /* 防止被压缩 */
}

/* 下方三个板块 - 精确计算高度 */
.bottom-row {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 0.5vw; /* 减少间距 */
    height: 46.5vh; /* 剩余高度 (92vh - 44vh - 0.5vh - 1vh padding) */
    flex-shrink: 0; /* 防止被压缩 */
}

/* 通用模块样式 - 确保所有模块都占满分配空间 */
.module {
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
    height: 100%; /* 占满网格高度 */
    display: flex;
    flex-direction: column;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 项目概览卡片样式 */
.project-card {
    background: linear-gradient(to bottom, #eef3ff 0%,#fff 50% , #fff 100%);
    border-radius: 8px;
    overflow: hidden;
    height: 100%; /* 占满网格高度 */
    display: flex;
    flex-direction: column;
}



.project-header img {
    width: 24%;
    float: left;
    margin-right: 20px;
}

.project-title {
    font-size: 14px;
    font-weight: bold;
    line-height: 1.4;
    margin-bottom: 10px;
    margin-top: 6px;
}

.status-badge {
    background: #52c41a;
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    display: inline-block;
}

.project-content {
    padding: 15px;
    flex: 1; /* 占用剩余空间 */
    overflow-y: auto; /* 内容超出时可滚动 */
    overflow-x: hidden;
}
.project-content img {
    float: left;
    margin-right: 6px;
}
.project-section-title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 15px;
    color: #000;
}

.company-name {
    border-radius: 6px;
    font-size: 12px;
    color: #666871;
    margin-bottom: 15px;
    line-height: 1.5;
}

.info-list {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    padding-bottom: 2px;
}

.info-label {
    color: #595959;
    font-size: 12px;
    font-weight: normal;
}

.info-value {
    color: #000;
    font-size: 12px;
    font-weight: 500;
}

/* 功能模块样式 - 更新为支持内部滚动 */
.module {
    background: #fff;
    border-radius: 8px;
    height: 100%; /* 占满网格高度 */
    display: flex;
    flex-direction: column;
    transition: transform 0.3s, box-shadow 0.3s;
    overflow: hidden; /* 防止内容溢出 */
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.module:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 模块标题区域 */
.module-header {
    padding: 15px 20px 10px 20px;
    border-bottom: 1px solid #f0f0f0;
    flex-shrink: 0; /* 防止标题被压缩 */
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.module-title {
    font-size: 16px;
    font-weight: bold;
    color: #262626;
    margin: 0;
}

/* 模块内容区域 - 支持内部滚动 */
.module-content {
    flex: 1; /* 占用剩余空间 */
    padding: 15px 20px;
    overflow-y: auto; /* 内容超出时可滚动 */
    overflow-x: hidden;
    min-height: 0; /* 确保flex子项可以收缩 */
    display: flex;
    flex-direction: column;
}

/* 图表主体区域 */
.chart-body {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    min-height: 0;
}

/* 流程列表区域 */
.process-list {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    min-height: 0;
}

/* 时间轴容器 */
.timeline-container {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    min-height: 0;
}

.module-icon {
    width: 50px;
    height: 50px;
    background: #e6f7ff;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 15px;
    color: #1890ff;
    font-size: 20px;
}

.stat-value {
    font-size: 32px;
    font-weight: bold;
    color: #1890ff;
    margin-bottom: 10px;
    text-align: center;
}

.stat-label {
    font-size: 14px;
    color: #666;
    text-align: center;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .nav-container {
        height: 10vh; /* 在中等屏幕上增加导航栏高度 */
    }

    .main-content {
        height: 90vh; /* 相应调整主内容高度 */
    }

    .top-row {
        height: 40vh; /* 调整上方区域高度 */
    }

    .bottom-row {
        height: 48vh; /* 调整下方区域高度 */
    }
}

@media (max-width: 992px) {
    .top-row {
        grid-template-columns: repeat(2, 1fr);
        height: 45vh; /* 在平板上增加高度 */
    }

    .bottom-row {
        height: 43vh; /* 相应调整 */
    }

    .three-column-layout {
        flex-direction: column;
        gap: 1vh;
    }

    .column-1, .column-2, .column-3 {
        flex: none;
        height: 30vh; /* 垂直排列时的固定高度 */
    }
}

@media (max-width: 768px) {
    .nav-container {
        height: 12vh; /* 在小屏幕上进一步增加导航栏高度 */
    }

    .main-content {
        height: 88vh;
        padding: 0.5vh 1vw; /* 减少内边距 */
    }

    .nav-menu {
        display: none; /* 在小屏幕上隐藏导航菜单 */
    }

    .search-box {
        display: none; /* 在小屏幕上隐藏搜索框 */
    }
}

@media (max-width: 576px) {
    .top-row {
        grid-template-columns: 1fr;
        height: 50vh; /* 单列布局时增加高度 */
        gap: 0.5vh;
    }

    .bottom-row {
        height: 36vh; /* 相应调整 */
    }

    .three-column-layout {
        flex-direction: column;
        gap: 0.5vh;
    }

    .column-1, .column-2, .column-3 {
        flex: none;
        height: 25vh; /* 移动端的固定高度 */
    }

    .info-item {
        flex-direction: column;
        gap: 5px;
    }

    .chart-legend {
        float: none;
        max-width: 100%;
        justify-content: center;
        margin-top: 10px;
        gap: 8px;
    }

    .expense-grid {
        grid-template-columns: 1fr;
    }

    .column {
        padding: 1vh 0.5vw; /* 减少内边距 */
    }
}

.title {
    font-size: 16px;
    font-weight: 600;
    color: #1a1a1a;
    margin-bottom: 10px;
}

/* 回款进度区域 */
.payment-progress {
    display: flex;
    align-items: center;

    border-radius: 10px;
}

.chart-container {
    position: relative;
    width: 140px;
    height: 140px;
    margin-right: 20px;
}

canvas {
    display: block;
}

.chart-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
}

.percentage {
    font-size: 24px;
    font-weight: bold;
    color: #1890ff;
    line-height: 1;
    margin-bottom: 5px;
}

.payment-info {
    flex: 1;
}

.amount-row {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
}

.amount-label {
    font-size: 14px;
    color: #333;
    margin-right: 10px;
    min-width: 60px;
}

.amount-value {
    font-size: 14px;
    font-weight: 600;
}

.total {
    color: #722ed1;
}

.paid {
    color: #52c41a;
}

.unpaid {
    color: #ff4d4f;
}

/* 开票流程 */
.invoice-workflows {
    margin-top: 20px;
}

.section-title {
    font-size: 16px;
    font-weight: 600;
    color: #1a1a1a;
    padding-bottom: 8px;
    margin-bottom: 10px;
}

.workflow-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.workflow-item {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 8px 12px;
    transition: all 0.2s ease;
}

.workflow-item:hover {
    background: #e9ecef;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.workflow-title {
    font-size: 12px;
    color: #333;
    font-weight: 500;
}

.workflow-link {
    color: #007bff;
    text-decoration: none;
    transition: color 0.2s ease;
}

.workflow-link:hover {
    color: #0056b3;
    text-decoration: underline;
}

.no-data {
    color: #999;
    font-size: 12px;
    text-align: center;
    padding: 20px;
}

/* 计划回款日期 */
.payment-plan {
    margin-top: 20px;
}

.plan-title {
    font-size: 16px;
    font-weight: 600;
    color: #1a1a1a;
    padding-bottom: 8px;
}

.plan-list {
    list-style: none;
}

.plan-item {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
}

.plan-item:last-child {
    border-bottom: none;
}

.task-name {
    font-size: 12px;
    color: #595959;
}

.task-date {
    font-size: 12px;
    color: #000;
}


.task-assignment-container .header {
    background: white;
}

.task-assignment-container .title {
    font-size: 16px;
    font-weight: 600;
    color: #1a1a1a;
}



.task-assignment-container .info-section {
    margin-bottom: 20px;
}

.task-assignment-container .info-row {
    display: flex;
    margin-bottom: 15px;
    align-items: flex-start;
}

.task-assignment-container .info-label {
    font-size: 12px;
    font-weight: 500;
    color: #333;
    flex-shrink: 0;
}

.task-assignment-container .info-value {
    flex: 1;
    font-size: 12px;
    color: #666;
}

.task-assignment-container .team-members {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.task-assignment-container .member-tag {
    background: #eaf2fe;
    color: #4377ef;
    padding:2px;
    border-radius: 2px;
    font-size: 12px;
}

.task-assignment-container .change-info {
    color: #ff4d4f;
    font-size: 12px;
}

.task-assignment-container .subtasks-section {
    margin-top: 10px;
    flex: 1; /* 占用剩余空间 */
    overflow-y: auto; /* 内容超出时可滚动 */
    overflow-x: hidden;
    min-height: 0; /* 确保flex子项可以收缩 */
}

/* 人员变更列表样式 */
.task-assignment-container .change-list {
    max-height: 120px;
    overflow-y: auto;
    overflow-x: hidden;
}

.task-assignment-container .change-item {
    background: #fff2f0;
    border: 1px solid #ffccc7;
    border-radius: 4px;
    padding: 8px;
    margin-bottom: 8px;
    font-size: 11px;
}

.task-assignment-container .change-item:last-child {
    margin-bottom: 0;
}

.task-assignment-container .change-date {
    color: #6c757d;
    font-size: 10px;
    margin-top: 4px;
    font-style: italic;
}

/* 优化后的子任务列表样式 */
.task-assignment-container .subtasks-section {
    margin-top: 20px;
}

.task-assignment-container .section-header {
    margin-bottom: 15px;
}

.task-assignment-container .subtasks-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 16px;
    font-weight: 600;
    color: #1a1a1a;
    margin: 0;
}

.task-assignment-container .title-icon {
    font-size: 18px;
}

.task-assignment-container .task-count {
    font-size: 12px;
    color: #666;
    font-weight: normal;
    background: #f0f0f0;
    padding: 2px 6px;
    border-radius: 10px;
}

.task-assignment-container .subtasks-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 15px;
}

.task-assignment-container .subtask-card {
    background: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 15px;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.task-assignment-container .subtask-card:hover {
    border-color: #007bff;
    box-shadow: 0 4px 8px rgba(0,123,255,0.1);
    transform: translateY(-2px);
}

.task-assignment-container .subtask-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 12px;
}

.task-assignment-container .task-index {
    background: #007bff;
    color: white;
    font-size: 10px;
    font-weight: bold;
    padding: 4px 8px;
    border-radius: 12px;
    min-width: 30px;
    text-align: center;
}

.task-assignment-container .task-name {
    flex: 1;
    font-weight: 500;
    font-size: 14px;
}

.task-assignment-container .task-link {
    color: #007bff;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 5px;
}

.task-assignment-container .task-link:hover {
    text-decoration: underline;
}

.task-assignment-container .link-icon {
    font-size: 12px;
}

.task-assignment-container .task-owner-section {
    margin-bottom: 10px;
}

.task-assignment-container .owner-label {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 11px;
    color: #666;
    margin-bottom: 5px;
}

.task-assignment-container .owner-icon {
    font-size: 12px;
}

.task-assignment-container .task-owner {
    margin-bottom: 5px;
}

.task-assignment-container .owner-tag {
    background: #e3f2fd;
    color: #1976d2;
    font-size: 11px;
    font-weight: 500;
    padding: 3px 8px;
    border-radius: 12px;
    border: 1px solid #bbdefb;
}

.task-assignment-container .task-members-section {
    margin-bottom: 10px;
}

.task-assignment-container .members-label {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 11px;
    color: #666;
    margin-bottom: 5px;
}

.task-assignment-container .members-icon {
    font-size: 12px;
}

.task-assignment-container .all-members {
    font-size: 11px;
    color: #333;
    background: #f8f9fa;
    padding: 4px 8px;
    border-radius: 4px;
}

.task-assignment-container .task-date {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 11px;
    color: #666;
    margin-top: 8px;
    padding-top: 8px;
    border-top: 1px solid #f0f0f0;
}

.task-assignment-container .date-icon {
    font-size: 12px;
}

.task-assignment-container .no-tasks {
    grid-column: 1 / -1;
    text-align: center;
    padding: 40px 20px;
    color: #999;
    font-size: 14px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
}

.task-assignment-container .no-tasks-icon {
    font-size: 24px;
    opacity: 0.5;
}

.task-assignment-container .subtasks-title {
    font-size: 16px;
    font-weight: 600;
    color: #1a1a1a;
    padding-bottom: 10px;
}

.task-assignment-container .task-list {
    list-style: none;
}

.task-assignment-container .task-item {
    background: #f9f9f9;
    border-radius: 8px;
    padding: 7px 10px;
    margin-bottom: 8px;
    border-left: 4px solid #05c350;
}

.task-assignment-container .task-name {
    font-size: 12px;
    font-weight: 500;
    color: #1a1a1a;
    margin-bottom: 4px;
}

.task-assignment-container .task-link {
    color: #007bff;
    text-decoration: none;
    transition: color 0.2s ease;
}

.task-assignment-container .task-link:hover {
    color: #0056b3;
    text-decoration: underline;
}

.task-assignment-container .task-detail {
    font-size: 10px;
    color: #666;
    margin-bottom: 4px;
    display: flex;
    align-items: center;
}

.task-assignment-container .detail-label {
    font-weight: 500;
    margin-right: 8px;
    min-width: 120px;
}

.task-assignment-container .task-members {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 10px;
}

.task-assignment-container .member-badge {
    background: #f0f0f0;
    color: #666;
    padding: 2px;
    border-radius: 4px;
    font-size: 12px;
}

.change-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
    font-size: 12px;
}

.change-table th,
.change-table td {
    border: 1px solid #e0e0e0;
    padding: 8px 12px;
    text-align: left;
}

.change-table th {
    background-color: #f5f7fa;
    font-weight: 600;
    color: #333;
}

.change-table tr:nth-child(even) {
    background-color: #fafafa;
}

.change-table tr:hover {
    background-color: #f0f8ff;
}

.biaoti {
    font-size: 16px;
    margin-bottom: 10px;
}
.timeline {
    position: relative;
    padding-left: 20px;
    flex: 1; /* 占用剩余空间 */
    overflow-y: auto; /* 内容超出时可滚动 */
    overflow-x: hidden;
    padding-right: 5px; /* 为滚动条留出空间 */
}

/* 时间轴竖线 */
.timeline::before {
    content: '';
    position: absolute;
    left: 3px;
    top: 10px;
    height: calc(100% - 20px);
    width: 1px;
    background: #f0f0f0;
}

/* 优化后的时间线节点样式 */
.timeline-node {
    position: relative;
    display: flex;
    margin-bottom: 30px;
    align-items: flex-start;
}

.node-indicator {
    position: relative;
    margin-right: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.node-dot {
    width: 12px;
    height: 12px;
    border: 3px solid #4377ef;
    border-radius: 50%;
    background: white;
    z-index: 2;
    position: relative;
    box-shadow: 0 2px 4px rgba(67, 119, 239, 0.2);
}

.node-line {
    width: 2px;
    height: 40px;
    background: linear-gradient(to bottom, #4377ef, #e0e0e0);
    margin-top: 5px;
}

.node-content {
    flex: 1;
    background: #fff;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
    padding: 15px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    transition: all 0.2s ease;
}

.node-content:hover {
    border-color: #4377ef;
    box-shadow: 0 4px 8px rgba(67, 119, 239, 0.1);
}

.node-header {
    margin-bottom: 15px;
}

.node-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 16px;
    font-weight: 600;
    color: #222;
}

.milestone-icon {
    font-size: 18px;
}

.file-count {
    font-size: 12px;
    color: #666;
    font-weight: normal;
    background: #f0f4f8;
    padding: 2px 8px;
    border-radius: 10px;
}

.files-container {
    display: grid;
    gap: 10px;
}

.node-date {
    margin-left: 15px;
    font-size: 12px;
    color: #888;
    background: #f0f4f8;
    padding: 2px 8px;
    border-radius: 12px;
}

/* 优化后的文件卡片样式 */
.file-card {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 12px;
    transition: all 0.2s ease;
}

.file-card:hover {
    background: #e3f2fd;
    border-color: #4377ef;
    transform: translateY(-1px);
    box-shadow: 0 3px 8px rgba(67, 119, 239, 0.1);
}

.file-content {
    width: 100%;
}

.file-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 10px;
}

.file-title {
    display: flex;
    align-items: center;
    gap: 6px;
    flex: 1;
}

.file-icon {
    font-size: 14px;
    color: #4377ef;
}

.file-link {
    color: #333;
    text-decoration: none;
    font-weight: 500;
    font-size: 13px;
    line-height: 1.4;
}

.file-link:hover {
    color: #4377ef;
    text-decoration: underline;
}

.upload-date {
    display: flex;
    align-items: center;
    gap: 4px;
    background: #e3f2fd;
    color: #1976d2;
    font-size: 11px;
    font-weight: 500;
    padding: 4px 8px;
    border-radius: 12px;
    white-space: nowrap;
}

.date-icon {
    font-size: 10px;
}

.file-title {
    font-size: 12px;
    font-weight: 500;
    color: #4075ef;
}

.file-content {
    font-size: 14px;
    color: #999;
    line-height: 1.5;
}

footer {
    text-align: center;
    padding: 20px;
    font-size: 13px;
    color: #888;
    border-top: 1px solid #eee;
    background: #fafafa;
}
.three-column-layout {
    display: flex;
    width: 100%;
    gap: 1vw;
    height: 100%; /* 占满bottom-row的高度 */
}

.column {
    padding: 1.5vh 1vw; /* 使用视口单位的内边距 */
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.column-1, .column-2 {
    flex: 1; /* 各占1份 */
    background: #fff;
    border-radius: 8px;
    height: 100%; /* 占满三列布局的高度 */
}

.column-3 {
    flex: 2; /* 占2份，等于前两个的总和 */
    background: #fff;
    border-radius: 8px;
    height: 100%; /* 占满三列布局的高度 */
}

.column-header {
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 2px solid #3498db;
    display: flex;
    align-items: center;
}

.column-header i {
    margin-right: 10px;
    font-size: 20px;
    color: #3498db;
}

.content {
    flex-grow: 1;
}

.content p {
    margin-bottom: 15px;
    line-height: 1.6;
    color: #555;
}
.header h1  {
    font-size: 16px;
    margin-bottom: 15px;
}

.header-actions {
    display: flex;
    gap: 12px;
}

.btn {
    padding: 10px 20px;
    border-radius: 6px;
    border: none;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.btn-primary {
    background-color: #ff7c32;
    color: white;
    box-shadow: 0 4px 12px rgba(255, 124, 50, 0.3);
}

.btn-primary:hover {
    background-color: #ff6a1a;
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(255, 124, 50, 0.4);
}

.btn-secondary {
    background-color: #f0f2f5;
    color: #666;
}

.btn-secondary:hover {
    background-color: #e4e7eb;
}


.process-card {
    background: #fafafc;
    border-radius: 10px;
    padding: 8px 10px;
    display: flex;
    align-items: flex-start;
    transition: all 0.3s ease;
    margin-bottom: 18px;
    border-left: 5px solid #ff7c32;
}

.process-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
}

.process-content {
    flex: 1;
}

.process-title {
    font-size: 12px;
    font-weight: 600;
    margin-bottom: 4px;
    color: #1a1a1a;
    display: flex;
    align-items: center;
}

.process-title i {
    color: #ff7c32;
    font-size: 20px;
}

.workflow-link {
    color: #007bff;
    text-decoration: none;
    transition: color 0.2s ease;
}

.workflow-link:hover {
    color: #0056b3;
    text-decoration: underline;
}

.process-details {
    color: #666;
    font-size: 10px;
}

.process-meta {
    display: flex;
    gap: 20px;
    margin-top: 8px;
    margin-left: 30px;
}

.process-meta-item {
    display: flex;
    align-items: center;
    gap: 6px;
    color: #888;
    font-size: 14px;
}

.process-meta-item i {
    color: #ff7c32;
}

.process-actions {
    display: flex;
    gap: 10px;
}

.action-btn {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f5f7fa;
    color: #666;
    border: none;
    cursor: pointer;
    transition: all 0.2s;
}

.action-btn:hover {
    background: #ff7c32;
    color: white;
    transform: scale(1.1);
}
.expense-container {
    width: 100%;
    max-width: 900px;
    background: white;
    border-radius: 16px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
    overflow: hidden;
}

.expense-header {
    background: #fff;
}

.expense-header h1 {
    font-size: 16px;
    font-weight: 700;
    color: #1a1a1a;
}

.expense-content {
    display: flex;
    flex-wrap: wrap;
}

.expense-chart-container {
    flex: 1;
    min-width: 300px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
}

.expense-chart-wrapper {
    position: relative;
    width: 150px;
    height: 150px;
}

#expensePieChart {
    width: 100%;
    height: 100%;
}

.expense-chart-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
}

.expense-total-amount {
    font-size: 16px;
    font-weight: 700;
    color: #1a1a1a;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 80px;
}

.expense-total-label {
    font-size: 10px;
    color: #666;
    margin-top: 4px;
}

.expense-chart-legend {
    margin-top: 20px;
    text-align: center;
    font-size: 18px;
    font-weight: 600;
    color: #4285F4;
}

.expense-details-container {
    flex: 1;
    min-width: 300px;
    padding: 20px;
    overflow-y: auto; /* 内容超出时可滚动 */
    overflow-x: hidden;
}

.expense-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 8px;
    max-height: 200px;
    overflow-y: auto;
}


.expense-detail-info {
    flex: 1;
}
.expense-detail-icon {
    float: left;
    margin-right: 20px;
}
.expense-detail-item {
    padding: 8px;
    border-radius: 4px;
    background: #f9f9f9;
    transition: background-color 0.2s;
}

.expense-detail-item:hover {
    background: #f0f0f0;
}

.expense-detail-name {
    font-size: 11px;
    color: #1a1a1a;
    margin-bottom: 4px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    cursor: help;
}

.expense-detail-amount {
    font-size: 11px;
    font-weight: 700;
    color: #4285F4;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.expense-footer {
    padding: 24px;
    text-align: center;
    color: #666;
    font-size: 14px;
    border-top: 1px solid #eaeaea;
}

.chart-header h1 {
    font-size: 16px;
    font-weight: 700;
    color: #1a1a1a;
    margin-bottom: 15px;
    float: left;
}

.chart-legend {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    justify-content: flex-end;
    align-items: center;
    max-width: 60%;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 10px;
    white-space: nowrap;
}

.legend-color {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    flex-shrink: 0;
}

.legend-normal {
    background-color:  #05c350;
}

.legend-paused {
    background-color: #fe7e2e;
}

.legend-stopped {
    background-color: #c30505;
}

.legend-total {
    background-color: #4075ef;
}

.chart-body {
    width: 100%;
    position: relative;
    flex: 1; /* 占用剩余空间 */
    overflow-y: auto; /* 内容超出时可滚动 */
    overflow-x: hidden;
    padding-right: 5px; /* 为滚动条留出空间 */
}

.chart-grid {
    display: flex;
    height: 32px;
    margin-bottom: 15px;
    position: relative;
    align-items: center;
}

.row-label {
    width: 100px;
    padding-right: 15px;
    text-align: right;
    display: flex;
    flex-direction: column;
    justify-content: center;
    font-size: 12px;
    color: #333;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    cursor: help;
}

.progress-container {
    flex: 1;
    position: relative;
    height: 100%;
    background: #f0f2f5;
    border-radius: 60px;
    overflow: hidden;
}

.progress-bar {
    position: absolute;
    height: 100%;
    border-radius: 4px;
    display: flex;
    align-items: center;
    padding: 0 8px;
    font-weight: 600;
    color: white;
    box-sizing: border-box;
    min-width: 35px;
    transition: width 0.5s ease;
    font-size: 11px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.bar-normal {
    background-color: #05c350;
}

.bar-paused {
    background-color: #fe7e2e;
}

.bar-stopped {
    background-color: #c30505;
}

.bar-total {
    background-color: #4075ef;
    height: 40px;
}

.percentage-scale {
    position: absolute;
    top: 0;
    right: 0;
    height: 100%;
    display: flex;
    align-items: center;
    font-weight: 500;
    color: #555;
    font-size: 11px;
    padding-left: 8px;
    min-width: 35px;
    justify-content: center;
}