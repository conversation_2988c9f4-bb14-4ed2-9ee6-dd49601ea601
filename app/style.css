* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: "Microsoft YaHei", "Helvetica Neue", Arial, sans-serif;
}

body {
    background-color: #f5f6f8;
}

.nav-container {
    margin: 0 auto;
    background: #fff;
    border-radius: 4px;
    overflow: hidden;
}

/* 导航栏主体 */
.navbar {
    display: flex;
    align-items: center;
    padding: 0 20px;
    height: 64px;
    background: #fff;
}

/* Logo区域 */
.logo {
    display: flex;
    align-items: center;
    margin-right: 30px;
}

.logo-icon {
    width: 36px;
    height: 36px;
    background: #ffcc00;
    border-radius: 4px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    margin-right: 10px;
}

.logo-text {
    font-size: 16px;
    font-weight: bold;
    color: #000;
    white-space: nowrap;
}

/* 导航菜单 */
.nav-menu {
    display: flex;
    flex: 1;
    list-style: none;
}

.nav-item {
    margin-right: 2px;
}

.nav-item a {
    display: block;
    padding: 0 16px;
    height: 64px;
    line-height: 64px;
    text-decoration: none;
    color: #000;
    font-size: 14px;
    transition: all 0.3s;
}

.nav-item a:hover {
    background: #eaf2fe;
    color: #346cee;
}

.nav-item.active a {
    background: #eaf2fe;
    color: #346cee;
}

/* 搜索区域 */
.search-box {
    position: relative;
    margin-right: 20px;
}

.search-input {
    width: 180px;
    height: 36px;
    padding: 0 36px 0 12px;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    font-size: 14px;
    transition: all 0.3s;
}

.search-input:focus {
    border-color: #1890ff;
    outline: none;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.search-icon {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #999;
    cursor: pointer;
}

/* 用户按钮 */
.user-btn {
    width: 36px;
    height: 36px;
    background: #1890ff;
    color: #fff;
    border: none;
    border-radius: 50%;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s;
}

.user-btn:hover {
    background: #40a9ff;
    transform: scale(1.05);
}

/* 响应式设计 */
@media (max-width: 992px) {
    .nav-menu {
        overflow-x: auto;
        padding-bottom: 5px;
    }
    
    .search-box {
        display: none;
    }
}

.page-title {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 20px;
    color: #000;
}

/* 上方四个板块 */
.top-row {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 10px;
    margin-bottom: 10px;
}

/* 下方三个板块 */
.bottom-row {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
}

/* 项目概览卡片样式 */
.bianju {
    padding: 0 20px;
    margin-top: 10px;
}
.project-card {
    background: linear-gradient(to bottom, #eef3ff 0%,#fff 50% , #fff 100%);
    border-radius: 8px;
    overflow: hidden;
    grid-column: span 1;
}

.project-header {
    background-image:url(/img/lan.png) ;
    background-repeat: no-repeat;
    color: white;
    padding: 15px;
}

.project-header img {
    width: 24%;
    float: left;
    margin-right: 20px;
}

.project-title {
    font-size: 14px;
    font-weight: bold;
    line-height: 1.4;
    margin-bottom: 10px;
    margin-top: 6px;
}

.status-badge {
    background: #52c41a;
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    display: inline-block;
}

.project-content {
    padding: 15px;
}
.project-content img {
    float: left;
    margin-right: 6px;
}
.project-section-title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 15px;
    color: #000;
}

.company-name {
    border-radius: 6px;
    font-size: 12px;
    color: #666871;
    margin-bottom: 15px;
    line-height: 1.5;
}

.info-list {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    padding-bottom: 2px;
}

.info-label {
    color: #595959;
    font-size: 12px;
    font-weight: normal;
}

.info-value {
    color: #000;
    font-size: 12px;
    font-weight: 500;
}

/* 功能模块样式 */
.module {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    min-height: 180px;
    display: flex;
    flex-direction: column;
    transition: transform 0.3s, box-shadow 0.3s;
}

.module:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
}

.module-icon {
    width: 50px;
    height: 50px;
    background: #e6f7ff;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 15px;
    color: #1890ff;
    font-size: 20px;
}

.module-title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 10px;
    color: #000;
}

.module-content {
    font-size: 14px;
    color: #666;
    line-height: 1.5;
    flex-grow: 1;
}

.stat-value {
    font-size: 32px;
    font-weight: bold;
    color: #1890ff;
    margin-bottom: 10px;
    text-align: center;
}

.stat-label {
    font-size: 14px;
    color: #666;
    text-align: center;
}

/* 响应式设计 */
@media (max-width: 992px) {
    .top-row {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .bottom-row {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 576px) {
    .top-row, .bottom-row {
        grid-template-columns: 1fr;
    }
    
    .info-item {
        flex-direction: column;
        gap: 5px;
    }
}

.title {
    font-size: 16px;
    font-weight: 600;
    color: #1a1a1a;
    margin-bottom: 10px;
}

/* 回款进度区域 */
.payment-progress {
    display: flex;
    align-items: center;

    border-radius: 10px;
}

.chart-container {
    position: relative;
    width: 140px;
    height: 140px;
    margin-right: 20px;
}

canvas {
    display: block;
}

.chart-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
}

.percentage {
    font-size: 24px;
    font-weight: bold;
    color: #1890ff;
    line-height: 1;
    margin-bottom: 5px;
}

.payment-info {
    flex: 1;
}

.amount-row {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
}

.amount-label {
    font-size: 14px;
    color: #333;
    margin-right: 10px;
    min-width: 60px;
}

.amount-value {
    font-size: 14px;
    font-weight: 600;
}

.paid {
    color: #1890ff;
}

.unpaid {
    color: #ff4d4f;
}

/* 计划回款日期 */
.payment-plan {
    margin-top: 20px;
}

.plan-title {
    font-size: 16px;
    font-weight: 600;
    color: #1a1a1a;
    padding-bottom: 8px;
}

.plan-list {
    list-style: none;
}

.plan-item {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
}

.plan-item:last-child {
    border-bottom: none;
}

.task-name {
    font-size: 12px;
    color: #595959;
}

.task-date {
    font-size: 12px;
    color: #000;
}


.task-assignment-container .header {
    background: white;
}

.task-assignment-container .title {
    font-size: 16px;
    font-weight: 600;
    color: #1a1a1a;
}



.task-assignment-container .info-section {
    margin-bottom: 20px;
}

.task-assignment-container .info-row {
    display: flex;
    margin-bottom: 15px;
    align-items: flex-start;
}

.task-assignment-container .info-label {
    font-size: 12px;
    font-weight: 500;
    color: #333;
    flex-shrink: 0;
}

.task-assignment-container .info-value {
    flex: 1;
    font-size: 12px;
    color: #666;
}

.task-assignment-container .team-members {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.task-assignment-container .member-tag {
    background: #eaf2fe;
    color: #4377ef;
    padding:2px;
    border-radius: 2px;
    font-size: 12px;
}

.task-assignment-container .change-info {
    color: #ff4d4f;
    font-size: 12px;
}

.task-assignment-container .subtasks-section {
    margin-top: 10px;
}

.task-assignment-container .subtasks-title {
    font-size: 16px;
    font-weight: 600;
    color: #1a1a1a;
    padding-bottom: 10px;
}

.task-assignment-container .task-list {
    list-style: none;
}

.task-assignment-container .task-item {
    background: #f9f9f9;
    border-radius: 8px;
    padding: 7px 10px;
    margin-bottom: 8px;
    border-left: 4px solid #05c350;
}

.task-assignment-container .task-name {
    font-size: 12px;
    font-weight: 500;
    color: #1a1a1a;
    margin-bottom: 4px;
}

.task-assignment-container .task-detail {
    font-size: 10px;
    color: #666;
    margin-bottom: 4px;
    display: flex;
    align-items: center;
}

.task-assignment-container .detail-label {
    font-weight: 500;
    margin-right: 8px;
    min-width: 120px;
}

.task-assignment-container .task-members {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 10px;
}

.task-assignment-container .member-badge {
    background: #f0f0f0;
    color: #666;
    padding: 2px;
    border-radius: 4px;
    font-size: 12px;
}

.biaoti {
    font-size: 16px;
    margin-bottom: 10px;
}
.timeline {
    position: relative;
    padding-left: 20px;
}

/* 时间轴竖线 */
.timeline::before {
    content: '';
    position: absolute;
    left: 3px;
    top: 10px;
    height: calc(100% - 20px);
    width: 1px;
    background: #f0f0f0;
}

.timeline-node {
    position: relative;
    margin-bottom: 20px;
}

/* 节点圆圈 */
.timeline-node::before {
    content: '';
    position: absolute;
    left: -20px;
    top: 5px;
    width: 6px;
    height: 6px;
    border: 1px solid #4377ef;
    border-radius: 50%;
    background: white;
    z-index: 2;
}

.node-header {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.node-title {
    font-size: 14px;
    font-weight: 600;
    color: #222;
}

.node-date {
    margin-left: 15px;
    font-size: 12px;
    color: #888;
    background: #f0f4f8;
    padding: 2px 8px;
    border-radius: 12px;
}

.file-card {
    background: #eaf2fe;
    border-radius: 4px;
    padding: 2px;
    margin-bottom: 12px;
    transition: all 0.3s ease;
}

.file-card:hover {
    transform: translateX(5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.file-title {
    font-size: 12px;
    font-weight: 500;
    color: #4075ef;
}

.file-content {
    font-size: 14px;
    color: #999;
    line-height: 1.5;
}

footer {
    text-align: center;
    padding: 20px;
    font-size: 13px;
    color: #888;
    border-top: 1px solid #eee;
    background: #fafafa;
}
.three-column-layout {
    display: flex;
    width: 100%;
    gap: 10px;;
}

.column {
    padding: 20px;
    display: flex;
    flex-direction: column;
}

.column-1, .column-2 {
    flex: 1; /* 各占1份 */
    background: #fff;
    border-radius: 8px;
    height: 320px;
}

.column-3 {
    flex: 2; /* 占2份，等于前两个的总和 */
    background: #fff;
    border-radius: 8px;
    height: 320px;
}

.column-header {
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 2px solid #3498db;
    display: flex;
    align-items: center;
}

.column-header i {
    margin-right: 10px;
    font-size: 20px;
    color: #3498db;
}

.content {
    flex-grow: 1;
}

.content p {
    margin-bottom: 15px;
    line-height: 1.6;
    color: #555;
}
.header h1  {
    font-size: 16px;
    margin-bottom: 15px;
}

.header-actions {
    display: flex;
    gap: 12px;
}

.btn {
    padding: 10px 20px;
    border-radius: 6px;
    border: none;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.btn-primary {
    background-color: #ff7c32;
    color: white;
    box-shadow: 0 4px 12px rgba(255, 124, 50, 0.3);
}

.btn-primary:hover {
    background-color: #ff6a1a;
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(255, 124, 50, 0.4);
}

.btn-secondary {
    background-color: #f0f2f5;
    color: #666;
}

.btn-secondary:hover {
    background-color: #e4e7eb;
}


.process-card {
    background: #fafafc;
    border-radius: 10px;
    padding: 8px 10px;
    display: flex;
    align-items: flex-start;
    transition: all 0.3s ease;
    margin-bottom: 18px;
    border-left: 5px solid #ff7c32;
}

.process-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
}

.process-content {
    flex: 1;
}

.process-title {
    font-size: 12px;
    font-weight: 600;
    margin-bottom: 4px;
    color: #1a1a1a;
    display: flex;
    align-items: center;
}

.process-title i {
    color: #ff7c32;
    font-size: 20px;
}

.process-details {
    color: #666;
    font-size: 10px;
}

.process-meta {
    display: flex;
    gap: 20px;
    margin-top: 8px;
    margin-left: 30px;
}

.process-meta-item {
    display: flex;
    align-items: center;
    gap: 6px;
    color: #888;
    font-size: 14px;
}

.process-meta-item i {
    color: #ff7c32;
}

.process-actions {
    display: flex;
    gap: 10px;
}

.action-btn {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f5f7fa;
    color: #666;
    border: none;
    cursor: pointer;
    transition: all 0.2s;
}

.action-btn:hover {
    background: #ff7c32;
    color: white;
    transform: scale(1.1);
}
.expense-container {
    width: 100%;
    max-width: 900px;
    background: white;
    border-radius: 16px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
    overflow: hidden;
}

.expense-header {
    background: #fff;
}

.expense-header h1 {
    font-size: 16px;
    font-weight: 700;
    color: #1a1a1a;
}

.expense-content {
    display: flex;
    flex-wrap: wrap;
}

.expense-chart-container {
    flex: 1;
    min-width: 300px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
}

.expense-chart-wrapper {
    position: relative;
    width: 150px;
    height: 150px;
}

#expensePieChart {
    width: 100%;
    height: 100%;
}

.expense-chart-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
}

.expense-total-amount {
    font-size: 20px;
    font-weight: 700;
    color: #1a1a1a;
}

.expense-total-label {
    font-size: 12px;
    color: #666;
    margin-top: 4px;
}

.expense-chart-legend {
    margin-top: 20px;
    text-align: center;
    font-size: 18px;
    font-weight: 600;
    color: #4285F4;
}

.expense-details-container {
    flex: 1;
    min-width: 300px;
    padding: 20px;
}

.expense-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
}


.expense-detail-info {
    flex: 1;
}
.expense-detail-icon {
    float: left;
    margin-right: 20px;
}
.expense-detail-name {
    font-size: 12px;
    color: #1a1a1a;
    margin-bottom: 4px;
}

.expense-detail-amount {
    font-size: 12px;
    font-weight: 700;
    color: #4285F4;
}

.expense-footer {
    padding: 24px;
    text-align: center;
    color: #666;
    font-size: 14px;
    border-top: 1px solid #eaeaea;
}

.chart-header h1 {
    font-size: 16px;
    font-weight: 700;
    color: #1a1a1a;
    margin-bottom: 15px;
    float: left;
}

.chart-legend {
    display: flex;
    gap: 25px;
    margin-bottom: 20px;
    flex-wrap: wrap;
    float: right;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
}

.legend-color {
    width: 14px;
    height: 14px;
    border-radius: 50%;
}

.legend-normal {
    background-color:  #05c350;
}

.legend-paused {
    background-color: #fe7e2e;
}

.legend-stopped {
    background-color: #c30505;
}

.legend-total {
    background-color: #4075ef;
}

.chart-body {
    width: 100%;
    position: relative;
}

.chart-grid {
    display: flex;
    height: 32px;
    margin-bottom: 20px;
    position: relative;
}

.row-label {
    width: 100px;
    padding-right: 30px;
    text-align: right;
    display: flex;
    flex-direction: column;
    justify-content: center;
    font-size: 14px;
    color: #333;
}

.progress-container {
    flex: 1;
    position: relative;
    height: 100%;
    background: #f0f2f5;
    border-radius: 60px;
    overflow: hidden;
}

.progress-bar {
    position: absolute;
    height: 100%;
    border-radius: 4px;
    display: flex;
    align-items: center;
    padding: 0 12px;
    font-weight: 600;
    color: white;
    box-sizing: border-box;
    min-width: 40px;
    transition: width 0.5s ease;
    font-size: 13px;
}

.bar-normal {
    background-color: #05c350;
}

.bar-paused {
    background-color: #fe7e2e;
}

.bar-stopped {
    background-color: #c30505;
}

.bar-total {
    background-color: #4075ef;
    height: 40px;
}

.percentage-scale {
    position: absolute;
    top: 0;
    right: 0;
    height: 100%;
    display: flex;
    align-items: center;
    font-weight: 500;
    color: #555;
    font-size: 13px;
    padding-left: 10px;
}