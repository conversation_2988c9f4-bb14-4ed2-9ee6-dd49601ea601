"use client";

import React, { useEffect, useState } from 'react';
import { getProjectInfo, getRepaymentInfo, getTaskAssignment, getProjectProgress, getWorkflows, getRelatedFees, getDigitalAssets } from '@/services/api';

export default function TestPage() {
  const [projectInfo, setProjectInfo] = useState<any>(null);
  const [repaymentInfo, setRepaymentInfo] = useState<any>(null);
  const [taskInfo, setTaskInfo] = useState<any>(null);
  const [progressInfo, setProgressInfo] = useState<any>(null);
  const [workflowsInfo, setWorkflowsInfo] = useState<any>(null);
  const [feesInfo, setFeesInfo] = useState<any>(null);
  const [assetsInfo, setAssetsInfo] = useState<any>(null);

  useEffect(() => {
    const projectId = "2369";
    
    // 测试所有API
    getProjectInfo(projectId).then(data => {
      console.log('Project Info:', data);
      setProjectInfo(data);
    });
    
    getRepaymentInfo(projectId).then(data => {
      console.log('Repayment Info:', data);
      setRepaymentInfo(data);
    });
    
    getTaskAssignment(projectId).then(data => {
      console.log('Task Assignment:', data);
      setTaskInfo(data);
    });
    
    getProjectProgress(projectId).then(data => {
      console.log('Project Progress:', data);
      setProgressInfo(data);
    });
    
    getWorkflows(projectId).then(data => {
      console.log('Workflows:', data);
      setWorkflowsInfo(data);
    });
    
    getRelatedFees(projectId).then(data => {
      console.log('Related Fees:', data);
      setFeesInfo(data);
    });
    
    getDigitalAssets(projectId).then(data => {
      console.log('Digital Assets:', data);
      setAssetsInfo(data);
    });
  }, []);

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h1>API 测试页面</h1>
      
      <div style={{ marginBottom: '20px' }}>
        <h2>项目信息</h2>
        <pre style={{ background: '#f5f5f5', padding: '10px', overflow: 'auto' }}>
          {JSON.stringify(projectInfo, null, 2)}
        </pre>
      </div>
      
      <div style={{ marginBottom: '20px' }}>
        <h2>回款信息</h2>
        <pre style={{ background: '#f5f5f5', padding: '10px', overflow: 'auto' }}>
          {JSON.stringify(repaymentInfo, null, 2)}
        </pre>
      </div>
      
      <div style={{ marginBottom: '20px' }}>
        <h2>任务分配</h2>
        <pre style={{ background: '#f5f5f5', padding: '10px', overflow: 'auto' }}>
          {JSON.stringify(taskInfo, null, 2)}
        </pre>
      </div>
      
      <div style={{ marginBottom: '20px' }}>
        <h2>项目进度</h2>
        <pre style={{ background: '#f5f5f5', padding: '10px', overflow: 'auto' }}>
          {JSON.stringify(progressInfo, null, 2)}
        </pre>
      </div>
      
      <div style={{ marginBottom: '20px' }}>
        <h2>工作流程</h2>
        <pre style={{ background: '#f5f5f5', padding: '10px', overflow: 'auto' }}>
          {JSON.stringify(workflowsInfo, null, 2)}
        </pre>
      </div>
      
      <div style={{ marginBottom: '20px' }}>
        <h2>相关费用</h2>
        <pre style={{ background: '#f5f5f5', padding: '10px', overflow: 'auto' }}>
          {JSON.stringify(feesInfo, null, 2)}
        </pre>
      </div>
      
      <div style={{ marginBottom: '20px' }}>
        <h2>数字资产</h2>
        <pre style={{ background: '#f5f5f5', padding: '10px', overflow: 'auto' }}>
          {JSON.stringify(assetsInfo, null, 2)}
        </pre>
      </div>
    </div>
  );
}
